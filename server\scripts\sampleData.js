const mongoose = require('mongoose');
const User = require('../models/User');
const Teacher = require('../models/Teacher');
const Subject = require('../models/Subject');
const Room = require('../models/Room');
const Class = require('../models/Class');

// Sample data for testing the enhanced models
const sampleData = {
  // Sample Classes with custom fields and colors
  classes: [
    {
      name: 'Grade 10 A',
      nameSinhala: '10 අ',
      grade: 10,
      section: 'A',
      capacity: 35,
      color: '#2196F3', // Blue
      customFields: [
        {
          name: 'Class Motto',
          value: 'Excellence in Learning',
          type: 'text'
        },
        {
          name: 'Student Count',
          value: 32,
          type: 'number'
        },
        {
          name: 'Has Smart Board',
          value: true,
          type: 'boolean'
        }
      ],
      preferences: {
        preferredPeriods: [
          {
            day: 'monday',
            periods: [1, 2, 3]
          }
        ],
        avoidPeriods: [
          {
            day: 'friday',
            periods: [7, 8],
            reason: 'Sports activities'
          }
        ]
      }
    },
    {
      name: 'Grade 11 B',
      nameSinhala: '11 ආ',
      grade: 11,
      section: 'B',
      capacity: 30,
      color: '#4CAF50', // Green
      customFields: [
        {
          name: 'Stream',
          value: 'Science',
          type: 'select',
          options: ['Science', 'Commerce', 'Arts']
        }
      ]
    }
  ],

  // Sample Subjects with enhanced fields
  subjects: [
    {
      name: 'Mathematics',
      nameSinhala: 'ගණිතය',
      code: 'MATH10',
      department: 'mathematics',
      grade: 10,
      periodsPerWeek: 6,
      duration: 40,
      color: '#FF5722', // Deep Orange
      isCore: true,
      customFields: [
        {
          name: 'Difficulty Level',
          value: 'Advanced',
          type: 'select',
          options: ['Basic', 'Intermediate', 'Advanced']
        },
        {
          name: 'Requires Calculator',
          value: true,
          type: 'boolean'
        }
      ],
      resources: {
        requiresProjector: true,
        requiresComputer: false,
        requiresLab: false
      },
      preferences: {
        preferredPeriods: [
          {
            day: 'monday',
            periods: [1, 2]
          },
          {
            day: 'wednesday',
            periods: [3, 4]
          }
        ],
        consecutivePeriods: true,
        maxConsecutive: 2
      }
    },
    {
      name: 'Physics',
      nameSinhala: 'භෞතික විද්‍යාව',
      code: 'PHY11',
      department: 'science',
      grade: 11,
      periodsPerWeek: 4,
      duration: 40,
      color: '#9C27B0', // Purple
      isCore: true,
      resources: {
        requiresLab: true,
        requiresProjector: true,
        specialEquipment: ['Oscilloscope', 'Multimeter']
      },
      customFields: [
        {
          name: 'Lab Sessions Per Week',
          value: 2,
          type: 'number'
        }
      ]
    }
  ],

  // Sample Rooms/Classrooms with enhanced fields
  rooms: [
    {
      number: 'A101',
      name: 'Science Laboratory 1',
      nameSinhala: 'විද්‍යා රසායනාගාරය 1',
      type: 'laboratory',
      building: 'Main Building',
      floor: 1,
      capacity: 30,
      color: '#00BCD4', // Cyan
      facilities: {
        hasProjector: true,
        hasComputers: false,
        hasAirConditioning: true,
        hasWhiteboard: true,
        hasSmartBoard: false,
        hasAudioSystem: true,
        specialEquipment: ['Fume Hood', 'Emergency Shower', 'Gas Outlets']
      },
      customFields: [
        {
          name: 'Lab Type',
          value: 'Chemistry',
          type: 'select',
          options: ['Chemistry', 'Physics', 'Biology']
        },
        {
          name: 'Safety Rating',
          value: 'A',
          type: 'text'
        },
        {
          name: 'Last Maintenance',
          value: '2024-01-15',
          type: 'date'
        }
      ]
    },
    {
      number: 'B205',
      name: 'Computer Laboratory',
      nameSinhala: 'පරිගණක රසායනාගාරය',
      type: 'computer_lab',
      building: 'Technology Block',
      floor: 2,
      capacity: 25,
      color: '#607D8B', // Blue Grey
      facilities: {
        hasProjector: true,
        hasComputers: true,
        computerCount: 25,
        hasAirConditioning: true,
        hasWhiteboard: true,
        hasSmartBoard: true,
        hasAudioSystem: true
      },
      customFields: [
        {
          name: 'Operating System',
          value: 'Windows 11',
          type: 'text'
        },
        {
          name: 'Network Speed',
          value: '1 Gbps',
          type: 'text'
        }
      ]
    }
  ],

  // Sample Teachers with enhanced personal information
  teachers: [
    {
      employeeId: 'T001',
      personalInfo: {
        title: 'Mr.',
        dateOfBirth: new Date('1985-05-15'),
        gender: 'male',
        nationality: 'Sri Lankan',
        nic: '198513601234',
        emergencyContact: {
          name: 'Jane Doe',
          relationship: 'Spouse',
          phone: '+94771234567'
        }
      },
      qualifications: [
        {
          degree: 'B.Sc. Mathematics',
          institution: 'University of Colombo',
          year: 2008,
          grade: 'First Class'
        },
        {
          degree: 'M.Sc. Applied Mathematics',
          institution: 'University of Peradeniya',
          year: 2010,
          grade: 'Distinction'
        }
      ],
      experience: {
        totalYears: 15,
        currentSchoolYears: 8,
        previousSchools: [
          {
            name: 'Royal College',
            position: 'Mathematics Teacher',
            startDate: new Date('2008-01-01'),
            endDate: new Date('2016-12-31')
          }
        ]
      },
      color: '#FF9800', // Orange
      customFields: [
        {
          name: 'Specialization',
          value: 'Calculus',
          type: 'text'
        },
        {
          name: 'Years of Experience',
          value: 15,
          type: 'number'
        },
        {
          name: 'Available for Extra Classes',
          value: true,
          type: 'boolean'
        }
      ],
      workingHours: {
        maxPeriodsPerDay: 6,
        maxPeriodsPerWeek: 30,
        preferredPeriods: [
          {
            day: 'monday',
            periods: [1, 2, 3, 4]
          }
        ],
        unavailablePeriods: [
          {
            day: 'friday',
            periods: [7, 8],
            reason: 'Department meeting'
          }
        ]
      },
      specialRoles: ['department_head']
    }
  ]
};

module.exports = sampleData;
