# School Timetable System - Enhanced API Documentation

## Overview
This document describes the enhanced API endpoints for the School Timetable System with support for custom fields, color coding, and advanced features as shown in your wizard interface.

## Enhanced Models

### 1. Classes API

#### Create Class with Custom Fields
```http
POST /api/classes
Content-Type: application/json
Authorization: Bearer <admin_token>

{
  "name": "Grade 10 A",
  "nameSinhala": "10 අ",
  "grade": 10,
  "section": "A",
  "capacity": 35,
  "color": "#2196F3",
  "customFields": [
    {
      "name": "Class Motto",
      "value": "Excellence in Learning",
      "type": "text"
    },
    {
      "name": "Student Count",
      "value": 32,
      "type": "number"
    },
    {
      "name": "Has Smart Board",
      "value": true,
      "type": "boolean"
    },
    {
      "name": "Stream",
      "value": "Science",
      "type": "select",
      "options": ["Science", "Commerce", "Arts"]
    }
  ],
  "preferences": {
    "preferredPeriods": [
      {
        "day": "monday",
        "periods": [1, 2, 3]
      }
    ],
    "avoidPeriods": [
      {
        "day": "friday",
        "periods": [7, 8],
        "reason": "Sports activities"
      }
    ]
  }
}
```

### 2. Subjects API

#### Create Subject with Enhanced Features
```http
POST /api/subjects
Content-Type: application/json
Authorization: Bearer <admin_token>

{
  "name": "Mathematics",
  "nameSinhala": "ගණිතය",
  "code": "MATH10",
  "department": "mathematics",
  "grade": 10,
  "periodsPerWeek": 6,
  "duration": 40,
  "color": "#FF5722",
  "isCore": true,
  "customFields": [
    {
      "name": "Difficulty Level",
      "value": "Advanced",
      "type": "select",
      "options": ["Basic", "Intermediate", "Advanced"]
    },
    {
      "name": "Requires Calculator",
      "value": true,
      "type": "boolean"
    }
  ],
  "resources": {
    "requiresProjector": true,
    "requiresComputer": false,
    "requiresLab": false,
    "specialEquipment": ["Scientific Calculator", "Geometry Set"]
  },
  "preferences": {
    "preferredPeriods": [
      {
        "day": "monday",
        "periods": [1, 2]
      }
    ],
    "consecutivePeriods": true,
    "maxConsecutive": 2
  }
}
```

### 3. Rooms/Classrooms API

#### Create Room with Custom Fields
```http
POST /api/rooms
Content-Type: application/json
Authorization: Bearer <admin_token>

{
  "number": "A101",
  "name": "Science Laboratory 1",
  "nameSinhala": "විද්‍යා රසායනාගාරය 1",
  "type": "laboratory",
  "building": "Main Building",
  "floor": 1,
  "capacity": 30,
  "color": "#00BCD4",
  "facilities": {
    "hasProjector": true,
    "hasComputers": false,
    "hasAirConditioning": true,
    "hasWhiteboard": true,
    "hasSmartBoard": false,
    "hasAudioSystem": true,
    "specialEquipment": ["Fume Hood", "Emergency Shower", "Gas Outlets"]
  },
  "customFields": [
    {
      "name": "Lab Type",
      "value": "Chemistry",
      "type": "select",
      "options": ["Chemistry", "Physics", "Biology"]
    },
    {
      "name": "Safety Rating",
      "value": "A",
      "type": "text"
    },
    {
      "name": "Last Maintenance",
      "value": "2024-01-15",
      "type": "date"
    }
  ]
}
```

### 4. Teachers API

#### Create Teacher with Enhanced Personal Information
```http
POST /api/teachers
Content-Type: application/json
Authorization: Bearer <admin_token>

{
  "employeeId": "T001",
  "user": "user_object_id_here",
  "personalInfo": {
    "title": "Mr.",
    "dateOfBirth": "1985-05-15",
    "gender": "male",
    "nationality": "Sri Lankan",
    "nic": "198513601234",
    "emergencyContact": {
      "name": "Jane Doe",
      "relationship": "Spouse",
      "phone": "+94771234567"
    }
  },
  "qualifications": [
    {
      "degree": "B.Sc. Mathematics",
      "institution": "University of Colombo",
      "year": 2008,
      "grade": "First Class"
    }
  ],
  "experience": {
    "totalYears": 15,
    "currentSchoolYears": 8
  },
  "color": "#FF9800",
  "customFields": [
    {
      "name": "Specialization",
      "value": "Calculus",
      "type": "text"
    },
    {
      "name": "Available for Extra Classes",
      "value": true,
      "type": "boolean"
    }
  ],
  "workingHours": {
    "maxPeriodsPerDay": 6,
    "maxPeriodsPerWeek": 30,
    "preferredPeriods": [
      {
        "day": "monday",
        "periods": [1, 2, 3, 4]
      }
    ]
  }
}
```

## Custom Field Types

The system supports the following custom field types:

1. **text**: Simple text input
2. **number**: Numeric values
3. **boolean**: True/false values
4. **date**: Date values (ISO 8601 format)
5. **select**: Dropdown with predefined options

## Color Coding

All entities support color coding using hex color codes (e.g., `#FF5722`). Colors are used for:
- Visual identification in timetables
- UI theming
- Category grouping

## Grade Selection

Classes and subjects support grades 1-13, matching the Sri Lankan education system:
- Grades 1-5: Primary
- Grades 6-9: Junior Secondary
- Grades 10-11: Senior Secondary
- Grades 12-13: Advanced Level

## API Endpoints Summary

### Classes
- `GET /api/classes` - List all classes
- `GET /api/classes/:id` - Get specific class
- `POST /api/classes` - Create new class
- `PUT /api/classes/:id` - Update class
- `DELETE /api/classes/:id` - Delete class

### Subjects
- `GET /api/subjects` - List all subjects
- `GET /api/subjects/:id` - Get specific subject
- `POST /api/subjects` - Create new subject
- `PUT /api/subjects/:id` - Update subject
- `DELETE /api/subjects/:id` - Delete subject

### Rooms
- `GET /api/rooms` - List all rooms
- `GET /api/rooms/:id` - Get specific room
- `POST /api/rooms` - Create new room
- `PUT /api/rooms/:id` - Update room
- `DELETE /api/rooms/:id` - Delete room

### Teachers
- `GET /api/teachers` - List all teachers
- `GET /api/teachers/:id` - Get specific teacher
- `POST /api/teachers` - Create new teacher
- `PUT /api/teachers/:id` - Update teacher
- `DELETE /api/teachers/:id` - Delete teacher

## Running the Sample Data Script

To populate your database with sample data:

```bash
cd server
node scripts/populateDatabase.js
```

This will create sample users, teachers, subjects, rooms, and classes with all the enhanced features demonstrated.
