const mongoose = require('mongoose');

// Custom field schema for flexible class properties
const customFieldSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  value: {
    type: mongoose.Schema.Types.Mixed,
    required: true
  },
  type: {
    type: String,
    enum: ['text', 'number', 'boolean', 'date', 'select'],
    default: 'text'
  },
  options: [String] // For select type fields
}, { _id: false });

const classSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  nameSinhala: {
    type: String,
    required: true,
    trim: true
  },
  // Short name/code for the class (as shown in screenshot)
  short: {
    type: String,
    required: true,
    trim: true,
    uppercase: true,
    maxlength: 10
  },
  grade: {
    type: Number,
    required: true,
    min: 1,
    max: 13
  },
  // Grade display name (Grade 1, Grade 2, etc.)
  gradeDisplay: {
    type: String,
    required: true,
    enum: [
      'Grade 1', 'Grade 2', 'Grade 3', 'Grade 4', 'Grade 5',
      'Grade 6', 'Grade 7', 'Grade 8', 'Grade 9', 'Grade 10',
      'Grade 11', 'Grade 12', 'Grade 13'
    ]
  },
  section: {
    type: String,
    required: true,
    trim: true,
    uppercase: true
  },
  academicYear: {
    type: Number,
    required: true,
    default: () => new Date().getFullYear()
  },
  classTeacher: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Teacher'
  },
  students: [{
    studentId: String,
    name: String,
    nameSinhala: String,
    rollNumber: Number,
    parent: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  }],
  subjects: [{
    subject: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Subject',
      required: true
    },
    teacher: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Teacher',
      required: true
    },
    periodsPerWeek: {
      type: Number,
      required: true,
      min: 1
    }
  }],
  capacity: {
    type: Number,
    required: true,
    min: 1,
    max: 50,
    default: 35
  },
  currentStrength: {
    type: Number,
    default: 0,
    min: 0
  },
  room: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Room'
  },
  schedule: {
    startTime: {
      type: String,
      default: '07:30'
    },
    endTime: {
      type: String,
      default: '13:30'
    },
    breakTimes: [{
      name: String,
      startTime: String,
      endTime: String
    }]
  },
  // Color for visual identification in timetables
  color: {
    type: String,
    default: '#2196F3', // Material Design Blue
    match: /^#[0-9A-F]{6}$/i
  },
  // Custom fields for additional class properties
  customFields: [customFieldSchema],
  // Class preferences and settings
  preferences: {
    preferredPeriods: [{
      day: {
        type: String,
        enum: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday']
      },
      periods: [Number]
    }],
    avoidPeriods: [{
      day: {
        type: String,
        enum: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday']
      },
      periods: [Number],
      reason: String
    }]
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Compound index for unique class per grade/section/year
classSchema.index({ grade: 1, section: 1, academicYear: 1 }, { unique: true });

// Other indexes
classSchema.index({ classTeacher: 1 });
classSchema.index({ academicYear: 1 });
classSchema.index({ grade: 1 });

// Virtual for full class name
classSchema.virtual('fullName').get(function() {
  return `${this.grade}${this.section}`;
});

classSchema.virtual('fullNameSinhala').get(function() {
  return this.nameSinhala || this.fullName;
});

// Pre-save middleware to update current strength and grade display
classSchema.pre('save', function(next) {
  this.currentStrength = this.students.length;

  // Auto-set gradeDisplay based on grade if not provided
  if (!this.gradeDisplay && this.grade) {
    this.gradeDisplay = `Grade ${this.grade}`;
  }

  // Auto-set short name if not provided
  if (!this.short && this.grade && this.section) {
    this.short = `${this.grade}${this.section}`;
  }

  next();
});

classSchema.set('toJSON', { virtuals: true });

module.exports = mongoose.model('Class', classSchema);
