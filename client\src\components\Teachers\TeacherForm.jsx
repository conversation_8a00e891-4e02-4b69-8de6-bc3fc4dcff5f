import React, { useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Box,
  Typography,
  Divider,
  FormControlLabel,
  Switch,
  Autocomplete,
  Alert
} from '@mui/material'
import { Add, Remove } from '@mui/icons-material'
import axios from 'axios'

const TeacherForm = ({ open, onClose, teacher, mode, onSuccess }) => {
  const { t, i18n } = useTranslation()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [subjects, setSubjects] = useState([])
  const [classes, setClasses] = useState([])
  const [users, setUsers] = useState([])

  const [formData, setFormData] = useState({
    user: '',
    employeeId: '',
    subjects: [],
    classes: [],
    qualifications: [{ degree: '', institution: '', year: '' }],
    experience: {
      totalYears: 0,
      currentSchoolYears: 0
    },
    workingHours: {
      maxPeriodsPerDay: 6,
      maxPeriodsPerWeek: 30,
      preferredPeriods: [],
      unavailablePeriods: []
    },
    isClassTeacher: false,
    classTeacherOf: '',
    specialRoles: [],
    isActive: true
  })

  const specialRoleOptions = [
    'principal',
    'vice_principal', 
    'department_head',
    'coordinator',
    'librarian',
    'counselor'
  ]

  const daysOfWeek = [
    'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'
  ]

  useEffect(() => {
    if (open) {
      fetchData()
      if (teacher && mode !== 'add') {
        setFormData({
          user: teacher.user?._id || '',
          employeeId: teacher.employeeId || '',
          subjects: teacher.subjects?.map(s => s._id) || [],
          classes: teacher.classes?.map(c => c._id) || [],
          qualifications: teacher.qualifications?.length ? teacher.qualifications : [{ degree: '', institution: '', year: '' }],
          experience: teacher.experience || { totalYears: 0, currentSchoolYears: 0 },
          workingHours: teacher.workingHours || {
            maxPeriodsPerDay: 6,
            maxPeriodsPerWeek: 30,
            preferredPeriods: [],
            unavailablePeriods: []
          },
          isClassTeacher: teacher.isClassTeacher || false,
          classTeacherOf: teacher.classTeacherOf?._id || '',
          specialRoles: teacher.specialRoles || [],
          isActive: teacher.isActive !== undefined ? teacher.isActive : true
        })
      } else {
        // Reset form for add mode
        setFormData({
          user: '',
          employeeId: '',
          subjects: [],
          classes: [],
          qualifications: [{ degree: '', institution: '', year: '' }],
          experience: { totalYears: 0, currentSchoolYears: 0 },
          workingHours: {
            maxPeriodsPerDay: 6,
            maxPeriodsPerWeek: 30,
            preferredPeriods: [],
            unavailablePeriods: []
          },
          isClassTeacher: false,
          classTeacherOf: '',
          specialRoles: [],
          isActive: true
        })
      }
    }
  }, [open, teacher, mode])

  const fetchData = async () => {
    try {
      const [subjectsRes, classesRes, usersRes] = await Promise.all([
        axios.get('/api/subjects'),
        axios.get('/api/classes'),
        axios.get('/api/auth/users') // Assuming this endpoint exists
      ])
      setSubjects(subjectsRes.data.subjects || [])
      setClasses(classesRes.data.classes || [])
      setUsers(usersRes.data.users || [])
    } catch (err) {
      console.error('Failed to fetch data:', err)
      setError('Failed to load form data')
    }
  }

  const handleChange = (field, value) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.')
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }))
    } else {
      setFormData(prev => ({ ...prev, [field]: value }))
    }
  }

  const handleQualificationChange = (index, field, value) => {
    const newQualifications = [...formData.qualifications]
    newQualifications[index] = { ...newQualifications[index], [field]: value }
    setFormData(prev => ({ ...prev, qualifications: newQualifications }))
  }

  const addQualification = () => {
    setFormData(prev => ({
      ...prev,
      qualifications: [...prev.qualifications, { degree: '', institution: '', year: '' }]
    }))
  }

  const removeQualification = (index) => {
    if (formData.qualifications.length > 1) {
      const newQualifications = formData.qualifications.filter((_, i) => i !== index)
      setFormData(prev => ({ ...prev, qualifications: newQualifications }))
    }
  }

  const handleSubmit = async () => {
    try {
      setLoading(true)
      setError(null)

      // Validate required fields
      if (!formData.user || !formData.employeeId) {
        setError('Please fill in all required fields')
        return
      }

      const url = mode === 'add' ? '/api/teachers' : `/api/teachers/${teacher._id}`
      const method = mode === 'add' ? 'post' : 'put'

      await axios[method](url, formData)
      
      onSuccess?.()
      onClose()
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to save teacher')
    } finally {
      setLoading(false)
    }
  }

  const getSubjectName = (subject) => {
    return i18n.language === 'si' ? subject.nameSinhala : subject.name
  }

  const getClassName = (classItem) => {
    return i18n.language === 'si' ? classItem.nameSinhala : classItem.name
  }

  const getUserName = (user) => {
    return `${user.profile?.firstName || ''} ${user.profile?.lastName || ''}`.trim() || user.email
  }

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        {mode === 'add' && t('teachers.add')}
        {mode === 'edit' && t('teachers.edit')}
        {mode === 'view' && t('common.view')}
      </DialogTitle>
      
      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Box sx={{ pt: 2 }}>
          <Grid container spacing={3}>
            {/* Basic Information */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Basic Information
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth required>
                <InputLabel>User Account</InputLabel>
                <Select
                  value={formData.user}
                  onChange={(e) => handleChange('user', e.target.value)}
                  label="User Account"
                  disabled={mode === 'view'}
                  MenuProps={{
                    PaperProps: {
                      style: {
                        maxHeight: 300,
                        width: 350,
                        zIndex: 9999,
                      },
                    },
                    anchorOrigin: {
                      vertical: 'bottom',
                      horizontal: 'left',
                    },
                    transformOrigin: {
                      vertical: 'top',
                      horizontal: 'left',
                    },
                  }}
                >
                  {users.map((user) => (
                    <MenuItem key={user._id} value={user._id}>
                      {getUserName(user)}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                required
                label={t('teachers.employeeId')}
                value={formData.employeeId}
                onChange={(e) => handleChange('employeeId', e.target.value)}
                disabled={mode === 'view'}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <Autocomplete
                multiple
                options={subjects}
                getOptionLabel={(option) => getSubjectName(option)}
                value={subjects.filter(s => formData.subjects.includes(s._id))}
                onChange={(event, newValue) => {
                  handleChange('subjects', newValue.map(v => v._id))
                }}
                disabled={mode === 'view'}
                renderTags={(value, getTagProps) =>
                  value.map((option, index) => (
                    <Chip
                      variant="outlined"
                      label={getSubjectName(option)}
                      {...getTagProps({ index })}
                      key={option._id}
                    />
                  ))
                }
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label={t('teachers.subjects')}
                    placeholder="Select subjects"
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <Autocomplete
                multiple
                options={classes}
                getOptionLabel={(option) => getClassName(option)}
                value={classes.filter(c => formData.classes.includes(c._id))}
                onChange={(event, newValue) => {
                  handleChange('classes', newValue.map(v => v._id))
                }}
                disabled={mode === 'view'}
                renderTags={(value, getTagProps) =>
                  value.map((option, index) => (
                    <Chip
                      variant="outlined"
                      label={getClassName(option)}
                      {...getTagProps({ index })}
                      key={option._id}
                    />
                  ))
                }
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label={t('teachers.classes')}
                    placeholder="Select classes"
                  />
                )}
              />
            </Grid>

            {/* Experience */}
            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
              <Typography variant="h6" gutterBottom>
                {t('teachers.experience')}
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                type="number"
                label="Total Years of Experience"
                value={formData.experience.totalYears}
                onChange={(e) => handleChange('experience.totalYears', parseInt(e.target.value) || 0)}
                disabled={mode === 'view'}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                type="number"
                label="Years at Current School"
                value={formData.experience.currentSchoolYears}
                onChange={(e) => handleChange('experience.currentSchoolYears', parseInt(e.target.value) || 0)}
                disabled={mode === 'view'}
              />
            </Grid>

            {/* Working Hours */}
            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
              <Typography variant="h6" gutterBottom>
                {t('teachers.workingHours')}
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                type="number"
                label="Max Periods Per Day"
                value={formData.workingHours.maxPeriodsPerDay}
                onChange={(e) => handleChange('workingHours.maxPeriodsPerDay', parseInt(e.target.value) || 0)}
                disabled={mode === 'view'}
                inputProps={{ min: 1, max: 10 }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                type="number"
                label="Max Periods Per Week"
                value={formData.workingHours.maxPeriodsPerWeek}
                onChange={(e) => handleChange('workingHours.maxPeriodsPerWeek', parseInt(e.target.value) || 0)}
                disabled={mode === 'view'}
                inputProps={{ min: 1, max: 50 }}
              />
            </Grid>

            {/* Class Teacher */}
            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
              <Typography variant="h6" gutterBottom>
                Class Teacher Information
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.isClassTeacher}
                    onChange={(e) => handleChange('isClassTeacher', e.target.checked)}
                    disabled={mode === 'view'}
                  />
                }
                label={t('teachers.classTeacher')}
              />
            </Grid>

            {formData.isClassTeacher && (
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Class Teacher Of</InputLabel>
                  <Select
                    value={formData.classTeacherOf}
                    onChange={(e) => handleChange('classTeacherOf', e.target.value)}
                    label="Class Teacher Of"
                    disabled={mode === 'view'}
                    MenuProps={{
                      PaperProps: {
                        style: {
                          maxHeight: 300,
                          width: 300,
                          zIndex: 9999,
                        },
                      },
                      anchorOrigin: {
                        vertical: 'bottom',
                        horizontal: 'left',
                      },
                      transformOrigin: {
                        vertical: 'top',
                        horizontal: 'left',
                      },
                    }}
                  >
                    {classes.map((classItem) => (
                      <MenuItem key={classItem._id} value={classItem._id}>
                        {getClassName(classItem)}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
            )}

            {/* Special Roles */}
            <Grid item xs={12}>
              <Autocomplete
                multiple
                options={specialRoleOptions}
                value={formData.specialRoles}
                onChange={(event, newValue) => handleChange('specialRoles', newValue)}
                disabled={mode === 'view'}
                renderTags={(value, getTagProps) =>
                  value.map((option, index) => (
                    <Chip
                      variant="outlined"
                      label={option.replace('_', ' ').toUpperCase()}
                      {...getTagProps({ index })}
                      key={option}
                    />
                  ))
                }
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Special Roles"
                    placeholder="Select special roles"
                  />
                )}
              />
            </Grid>

            {/* Qualifications */}
            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography variant="h6">
                  {t('teachers.qualifications')}
                </Typography>
                {mode !== 'view' && (
                  <Button
                    startIcon={<Add />}
                    onClick={addQualification}
                    size="small"
                  >
                    Add Qualification
                  </Button>
                )}
              </Box>
              
              {formData.qualifications.map((qualification, index) => (
                <Box key={index} sx={{ mb: 2, p: 2, border: '1px solid #e0e0e0', borderRadius: 1 }}>
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={4}>
                      <TextField
                        fullWidth
                        label="Degree"
                        value={qualification.degree}
                        onChange={(e) => handleQualificationChange(index, 'degree', e.target.value)}
                        disabled={mode === 'view'}
                      />
                    </Grid>
                    <Grid item xs={12} md={4}>
                      <TextField
                        fullWidth
                        label="Institution"
                        value={qualification.institution}
                        onChange={(e) => handleQualificationChange(index, 'institution', e.target.value)}
                        disabled={mode === 'view'}
                      />
                    </Grid>
                    <Grid item xs={12} md={3}>
                      <TextField
                        fullWidth
                        type="number"
                        label="Year"
                        value={qualification.year}
                        onChange={(e) => handleQualificationChange(index, 'year', e.target.value)}
                        disabled={mode === 'view'}
                      />
                    </Grid>
                    {mode !== 'view' && formData.qualifications.length > 1 && (
                      <Grid item xs={12} md={1}>
                        <Button
                          color="error"
                          onClick={() => removeQualification(index)}
                          sx={{ mt: 1 }}
                        >
                          <Remove />
                        </Button>
                      </Grid>
                    )}
                  </Grid>
                </Box>
              ))}
            </Grid>

            {/* Status */}
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.isActive}
                    onChange={(e) => handleChange('isActive', e.target.checked)}
                    disabled={mode === 'view'}
                  />
                }
                label={t('teachers.active')}
              />
            </Grid>
          </Grid>
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose}>
          {t('common.cancel')}
        </Button>
        {mode !== 'view' && (
          <Button
            variant="contained"
            onClick={handleSubmit}
            disabled={loading}
          >
            {loading ? 'Saving...' : t('common.save')}
          </Button>
        )}
      </DialogActions>
    </Dialog>
  )
}

export default TeacherForm
