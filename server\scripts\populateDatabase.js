const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const User = require('../models/User');
const Teacher = require('../models/Teacher');
const Subject = require('../models/Subject');
const Room = require('../models/Room');
const Class = require('../models/Class');
const sampleData = require('./sampleData');

// Database connection
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/school_timetable', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('MongoDB connected successfully');
  } catch (error) {
    console.error('MongoDB connection error:', error);
    process.exit(1);
  }
};

// Create sample users
const createSampleUsers = async () => {
  console.log('Creating sample users...');
  
  const users = [
    {
      username: 'admin',
      email: '<EMAIL>',
      password: 'admin123',
      role: 'admin',
      profile: {
        firstName: 'System',
        lastName: 'Administrator',
        firstNameSinhala: 'පද්ධති',
        lastNameSinhala: 'පරිපාලක',
        phone: '+94771234567'
      }
    },
    {
      username: 'teacher1',
      email: '<EMAIL>',
      password: 'teacher123',
      role: 'teacher',
      profile: {
        firstName: 'John',
        lastName: 'Doe',
        firstNameSinhala: 'ජෝන්',
        lastNameSinhala: 'ඩෝ',
        phone: '+94771234568'
      }
    },
    {
      username: 'teacher2',
      email: '<EMAIL>',
      password: 'teacher123',
      role: 'teacher',
      profile: {
        firstName: 'Jane',
        lastName: 'Smith',
        firstNameSinhala: 'ජේන්',
        lastNameSinhala: 'ස්මිත්',
        phone: '+94771234569'
      }
    }
  ];

  const createdUsers = [];
  for (const userData of users) {
    try {
      const existingUser = await User.findOne({ email: userData.email });
      if (!existingUser) {
        const user = new User(userData);
        await user.save();
        createdUsers.push(user);
        console.log(`Created user: ${user.username}`);
      } else {
        createdUsers.push(existingUser);
        console.log(`User already exists: ${existingUser.username}`);
      }
    } catch (error) {
      console.error(`Error creating user ${userData.username}:`, error.message);
    }
  }
  
  return createdUsers;
};

// Create sample subjects
const createSampleSubjects = async () => {
  console.log('Creating sample subjects...');
  
  const createdSubjects = [];
  for (const subjectData of sampleData.subjects) {
    try {
      const existingSubject = await Subject.findOne({ code: subjectData.code });
      if (!existingSubject) {
        const subject = new Subject(subjectData);
        await subject.save();
        createdSubjects.push(subject);
        console.log(`Created subject: ${subject.name} (${subject.code})`);
      } else {
        createdSubjects.push(existingSubject);
        console.log(`Subject already exists: ${existingSubject.name}`);
      }
    } catch (error) {
      console.error(`Error creating subject ${subjectData.name}:`, error.message);
    }
  }
  
  return createdSubjects;
};

// Create sample rooms
const createSampleRooms = async () => {
  console.log('Creating sample rooms...');
  
  const createdRooms = [];
  for (const roomData of sampleData.rooms) {
    try {
      const existingRoom = await Room.findOne({ number: roomData.number });
      if (!existingRoom) {
        const room = new Room(roomData);
        await room.save();
        createdRooms.push(room);
        console.log(`Created room: ${room.name} (${room.number})`);
      } else {
        createdRooms.push(existingRoom);
        console.log(`Room already exists: ${existingRoom.name}`);
      }
    } catch (error) {
      console.error(`Error creating room ${roomData.name}:`, error.message);
    }
  }
  
  return createdRooms;
};

// Create sample teachers
const createSampleTeachers = async (users) => {
  console.log('Creating sample teachers...');
  
  const teacherUsers = users.filter(user => user.role === 'teacher');
  const createdTeachers = [];
  
  for (let i = 0; i < sampleData.teachers.length && i < teacherUsers.length; i++) {
    const teacherData = { ...sampleData.teachers[i], user: teacherUsers[i]._id };
    
    try {
      const existingTeacher = await Teacher.findOne({ employeeId: teacherData.employeeId });
      if (!existingTeacher) {
        const teacher = new Teacher(teacherData);
        await teacher.save();
        createdTeachers.push(teacher);
        console.log(`Created teacher: ${teacherData.employeeId}`);
      } else {
        createdTeachers.push(existingTeacher);
        console.log(`Teacher already exists: ${existingTeacher.employeeId}`);
      }
    } catch (error) {
      console.error(`Error creating teacher ${teacherData.employeeId}:`, error.message);
    }
  }
  
  return createdTeachers;
};

// Create sample classes
const createSampleClasses = async (teachers, subjects, rooms) => {
  console.log('Creating sample classes...');
  
  const createdClasses = [];
  for (let i = 0; i < sampleData.classes.length; i++) {
    const classData = { ...sampleData.classes[i] };
    
    // Assign class teacher if available
    if (teachers.length > i) {
      classData.classTeacher = teachers[i]._id;
    }
    
    // Assign room if available
    if (rooms.length > i) {
      classData.room = rooms[i]._id;
    }
    
    // Assign subjects with teachers
    if (subjects.length > 0 && teachers.length > 0) {
      classData.subjects = subjects.slice(0, 2).map((subject, index) => ({
        subject: subject._id,
        teacher: teachers[index % teachers.length]._id,
        periodsPerWeek: subject.periodsPerWeek
      }));
    }
    
    try {
      const existingClass = await Class.findOne({
        grade: classData.grade,
        section: classData.section,
        academicYear: classData.academicYear || new Date().getFullYear()
      });
      
      if (!existingClass) {
        const classObj = new Class(classData);
        await classObj.save();
        createdClasses.push(classObj);
        console.log(`Created class: ${classObj.name}`);
      } else {
        createdClasses.push(existingClass);
        console.log(`Class already exists: ${existingClass.name}`);
      }
    } catch (error) {
      console.error(`Error creating class ${classData.name}:`, error.message);
    }
  }
  
  return createdClasses;
};

// Main function to populate database
const populateDatabase = async () => {
  try {
    await connectDB();
    
    console.log('Starting database population...');
    
    const users = await createSampleUsers();
    const subjects = await createSampleSubjects();
    const rooms = await createSampleRooms();
    const teachers = await createSampleTeachers(users);
    const classes = await createSampleClasses(teachers, subjects, rooms);
    
    console.log('\nDatabase population completed successfully!');
    console.log(`Created/Found: ${users.length} users, ${subjects.length} subjects, ${rooms.length} rooms, ${teachers.length} teachers, ${classes.length} classes`);
    
    process.exit(0);
  } catch (error) {
    console.error('Error populating database:', error);
    process.exit(1);
  }
};

// Run the script
if (require.main === module) {
  populateDatabase();
}

module.exports = { populateDatabase };
