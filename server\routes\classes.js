const express = require('express');
const { body, validationResult, query } = require('express-validator');
const { Class, Teacher, Subject } = require('../models');
const { authenticated, adminOnly } = require('../middleware/auth');

const router = express.Router();

// Get all classes
router.get('/', authenticated, [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('search').optional().trim(),
  query('grade').optional().isInt({ min: 1, max: 13 }),
  query('academicYear').optional().isInt({ min: 2020, max: 2030 }),
  query('active').optional().isBoolean()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: 'Validation errors',
        messageSinhala: 'වලංගුකරණ දෝෂ',
        errors: errors.array()
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;

    // Build filter
    const filter = {};
    if (req.query.active !== undefined) {
      filter.isActive = req.query.active === 'true';
    }
    if (req.query.grade) {
      filter.grade = parseInt(req.query.grade);
    }
    if (req.query.academicYear) {
      filter.academicYear = parseInt(req.query.academicYear);
    }
    if (req.query.search) {
      const searchRegex = new RegExp(req.query.search, 'i');
      filter.$or = [
        { name: searchRegex },
        { nameSinhala: searchRegex },
        { section: searchRegex }
      ];
    }

    const classes = await Class.find(filter)
      .populate('classTeacher', 'employeeId user')
      .populate('classTeacher.user', 'profile')
      .populate('room', 'number name nameSinhala type')
      .populate('subjects.subject', 'name nameSinhala code')
      .populate('subjects.teacher', 'employeeId user')
      .sort({ grade: 1, section: 1 })
      .skip(skip)
      .limit(limit);

    const total = await Class.countDocuments(filter);

    res.json({
      message: 'Classes retrieved successfully',
      messageSinhala: 'පන්ති සාර්ථකව ලබා ගන්නා ලදී',
      classes,
      pagination: {
        current: page,
        pages: Math.ceil(total / limit),
        total,
        limit
      }
    });
  } catch (error) {
    console.error('Get classes error:', error);
    res.status(500).json({
      message: 'Failed to retrieve classes',
      messageSinhala: 'පන්ති ලබා ගැනීමට අසමත් විය'
    });
  }
});

// Get class by ID
router.get('/:id', authenticated, async (req, res) => {
  try {
    const classData = await Class.findById(req.params.id)
      .populate('classTeacher', 'employeeId user qualifications')
      .populate('classTeacher.user', 'profile email')
      .populate('room', 'number name nameSinhala type facilities')
      .populate('subjects.subject', 'name nameSinhala code department periodsPerWeek')
      .populate('subjects.teacher', 'employeeId user')
      .populate('subjects.teacher.user', 'profile');

    if (!classData) {
      return res.status(404).json({
        message: 'Class not found',
        messageSinhala: 'පන්තිය සොයා ගත නොහැක'
      });
    }

    res.json({
      message: 'Class retrieved successfully',
      messageSinhala: 'පන්තිය සාර්ථකව ලබා ගන්නා ලදී',
      class: classData
    });
  } catch (error) {
    console.error('Get class error:', error);
    res.status(500).json({
      message: 'Failed to retrieve class',
      messageSinhala: 'පන්තිය ලබා ගැනීමට අසමත් විය'
    });
  }
});

// Create new class
router.post('/', adminOnly, [
  body('name').notEmpty().trim(),
  body('nameSinhala').notEmpty().trim(),
  body('grade').isInt({ min: 1, max: 13 }),
  body('section').notEmpty().trim(),
  body('academicYear').optional().isInt({ min: 2020, max: 2030 }),
  body('capacity').isInt({ min: 1, max: 50 }),
  body('classTeacher').optional().isMongoId(),
  body('room').optional().isMongoId(),
  body('color').optional().matches(/^#[0-9A-F]{6}$/i),
  body('customFields').optional().isArray(),
  body('customFields.*.name').optional().notEmpty().trim(),
  body('customFields.*.value').optional().exists(),
  body('customFields.*.type').optional().isIn(['text', 'number', 'boolean', 'date', 'select']),
  body('subjects').optional().isArray(),
  body('subjects.*.subject').isMongoId(),
  body('subjects.*.teacher').isMongoId(),
  body('subjects.*.periodsPerWeek').isInt({ min: 1, max: 10 }),
  body('preferences.preferredPeriods').optional().isArray(),
  body('preferences.avoidPeriods').optional().isArray()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: 'Validation errors',
        messageSinhala: 'වලංගුකරණ දෝෂ',
        errors: errors.array()
      });
    }

    // Check if class already exists for the same grade, section, and academic year
    const existingClass = await Class.findOne({
      grade: req.body.grade,
      section: req.body.section.toUpperCase(),
      academicYear: req.body.academicYear || new Date().getFullYear()
    });

    if (existingClass) {
      return res.status(400).json({
        message: 'Class already exists for this grade, section, and academic year',
        messageSinhala: 'මෙම ශ්‍රේණිය, අංශය සහ අධ්‍යයන වර්ෂය සඳහා පන්තිය දැනටමත් පවතී'
      });
    }

    const classData = new Class({
      ...req.body,
      section: req.body.section.toUpperCase()
    });
    
    await classData.save();

    const populatedClass = await Class.findById(classData._id)
      .populate('classTeacher', 'employeeId user')
      .populate('classTeacher.user', 'profile')
      .populate('room', 'number name nameSinhala type')
      .populate('subjects.subject', 'name nameSinhala code')
      .populate('subjects.teacher', 'employeeId user')
      .populate('subjects.teacher.user', 'profile');

    res.status(201).json({
      message: 'Class created successfully',
      messageSinhala: 'පන්තිය සාර්ථකව නිර්මාණය කරන ලදී',
      class: populatedClass
    });
  } catch (error) {
    console.error('Create class error:', error);
    res.status(500).json({
      message: 'Failed to create class',
      messageSinhala: 'පන්තිය නිර්මාණය කිරීමට අසමත් විය'
    });
  }
});

// Update class
router.put('/:id', adminOnly, [
  body('name').optional().notEmpty().trim(),
  body('nameSinhala').optional().notEmpty().trim(),
  body('grade').optional().isInt({ min: 1, max: 13 }),
  body('section').optional().notEmpty().trim(),
  body('capacity').optional().isInt({ min: 1, max: 50 }),
  body('classTeacher').optional().isMongoId(),
  body('room').optional().isMongoId(),
  body('subjects').optional().isArray(),
  body('subjects.*.subject').optional().isMongoId(),
  body('subjects.*.teacher').optional().isMongoId(),
  body('subjects.*.periodsPerWeek').optional().isInt({ min: 1, max: 10 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: 'Validation errors',
        messageSinhala: 'වලංගුකරණ දෝෂ',
        errors: errors.array()
      });
    }

    // If grade, section, or academic year is being changed, check for conflicts
    if (req.body.grade || req.body.section || req.body.academicYear) {
      const currentClass = await Class.findById(req.params.id);
      if (!currentClass) {
        return res.status(404).json({
          message: 'Class not found',
          messageSinhala: 'පන්තිය සොයා ගත නොහැක'
        });
      }

      const checkData = {
        grade: req.body.grade || currentClass.grade,
        section: (req.body.section || currentClass.section).toUpperCase(),
        academicYear: req.body.academicYear || currentClass.academicYear
      };

      const existingClass = await Class.findOne({
        ...checkData,
        _id: { $ne: req.params.id }
      });

      if (existingClass) {
        return res.status(400).json({
          message: 'Class already exists for this grade, section, and academic year',
          messageSinhala: 'මෙම ශ්‍රේණිය, අංශය සහ අධ්‍යයන වර්ෂය සඳහා පන්තිය දැනටමත් පවතී'
        });
      }
    }

    if (req.body.section) {
      req.body.section = req.body.section.toUpperCase();
    }

    const classData = await Class.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    ).populate('classTeacher', 'employeeId user')
     .populate('classTeacher.user', 'profile')
     .populate('room', 'number name nameSinhala type')
     .populate('subjects.subject', 'name nameSinhala code')
     .populate('subjects.teacher', 'employeeId user')
     .populate('subjects.teacher.user', 'profile');

    if (!classData) {
      return res.status(404).json({
        message: 'Class not found',
        messageSinhala: 'පන්තිය සොයා ගත නොහැක'
      });
    }

    res.json({
      message: 'Class updated successfully',
      messageSinhala: 'පන්තිය සාර්ථකව යාවත්කාලීන කරන ලදී',
      class: classData
    });
  } catch (error) {
    console.error('Update class error:', error);
    res.status(500).json({
      message: 'Failed to update class',
      messageSinhala: 'පන්තිය යාවත්කාලීන කිරීමට අසමත් විය'
    });
  }
});

// Delete class (soft delete)
router.delete('/:id', adminOnly, async (req, res) => {
  try {
    const classData = await Class.findByIdAndUpdate(
      req.params.id,
      { isActive: false },
      { new: true }
    );

    if (!classData) {
      return res.status(404).json({
        message: 'Class not found',
        messageSinhala: 'පන්තිය සොයා ගත නොහැක'
      });
    }

    res.json({
      message: 'Class deactivated successfully',
      messageSinhala: 'පන්තිය සාර්ථකව අක්‍රිය කරන ලදී'
    });
  } catch (error) {
    console.error('Delete class error:', error);
    res.status(500).json({
      message: 'Failed to deactivate class',
      messageSinhala: 'පන්තිය අක්‍රිය කිරීමට අසමත් විය'
    });
  }
});

module.exports = router;
