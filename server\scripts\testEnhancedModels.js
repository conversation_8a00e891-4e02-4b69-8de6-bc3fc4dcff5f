const mongoose = require('mongoose');
const User = require('../models/User');
const Teacher = require('../models/Teacher');
const Subject = require('../models/Subject');
const Room = require('../models/Room');
const Class = require('../models/Class');

// Test the enhanced models
const testEnhancedModels = async () => {
  try {
    // Connect to database
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/school_timetable', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('Connected to MongoDB');

    // Test 1: Create a class with custom fields
    console.log('\n=== Testing Class Model ===');
    const testClass = new Class({
      name: 'Test Class 12A',
      nameSinhala: 'පරීක්ෂණ පන්තිය 12අ',
      short: '12A',
      grade: 12,
      gradeDisplay: 'Grade 12',
      section: 'A',
      capacity: 30,
      color: '#E91E63',
      customFields: [
        {
          name: 'Stream',
          value: 'Physical Science',
          type: 'select',
          options: ['Physical Science', 'Biological Science', 'Commerce', 'Arts']
        },
        {
          name: 'Class Strength',
          value: 28,
          type: 'number'
        },
        {
          name: 'Has Air Conditioning',
          value: true,
          type: 'boolean'
        }
      ]
    });

    try {
      await testClass.save();
      console.log('✅ Class created successfully with custom fields');
      console.log('Class ID:', testClass._id);
      console.log('Custom Fields:', testClass.customFields);
    } catch (error) {
      if (error.code === 11000) {
        console.log('⚠️  Class already exists (duplicate key)');
      } else {
        console.log('❌ Error creating class:', error.message);
      }
    }

    // Test 2: Create a subject with preferences
    console.log('\n=== Testing Subject Model ===');
    const testSubject = new Subject({
      name: 'Advanced Chemistry',
      nameSinhala: 'උසස් රසායන විද්‍යාව',
      short: 'CHEM',
      code: 'CHEM12A',
      department: 'science',
      grade: 12,
      periodsPerWeek: 5,
      color: '#4CAF50',
      customFields: [
        {
          name: 'Lab Hours Per Week',
          value: 3,
          type: 'number'
        },
        {
          name: 'Requires Safety Equipment',
          value: true,
          type: 'boolean'
        }
      ],
      preferences: {
        preferredPeriods: [
          {
            day: 'tuesday',
            periods: [3, 4, 5]
          }
        ],
        consecutivePeriods: true,
        maxConsecutive: 2
      },
      resources: {
        requiresLab: true,
        requiresProjector: true,
        specialEquipment: ['Fume Hood', 'Chemical Storage']
      }
    });

    try {
      await testSubject.save();
      console.log('✅ Subject created successfully with preferences');
      console.log('Subject ID:', testSubject._id);
      console.log('Preferences:', testSubject.preferences);
    } catch (error) {
      if (error.code === 11000) {
        console.log('⚠️  Subject already exists (duplicate key)');
      } else {
        console.log('❌ Error creating subject:', error.message);
      }
    }

    // Test 3: Create a room with facilities
    console.log('\n=== Testing Room Model ===');
    const testRoom = new Room({
      classroomName: 'Advanced Chemistry Laboratory',
      number: 'LAB-CHEM-01',
      name: 'Advanced Chemistry Laboratory',
      nameSinhala: 'උසස් රසායන රසායනාගාරය',
      short: 'CHEM1',
      type: 'laboratory',
      nameClassroom: true,
      sharedRoom: false,
      mayBeClassroom: true,
      building: 'Science Block',
      floor: 2,
      capacity: 25,
      color: '#9C27B0',
      facilities: {
        hasProjector: true,
        hasComputers: false,
        hasAirConditioning: true,
        hasWhiteboard: true,
        hasSmartBoard: true,
        hasAudioSystem: false,
        specialEquipment: ['Fume Hood', 'Emergency Shower', 'Fire Extinguisher']
      },
      customFields: [
        {
          name: 'Safety Certification',
          value: 'ISO 14001',
          type: 'text'
        },
        {
          name: 'Last Safety Inspection',
          value: '2024-01-15',
          type: 'date'
        },
        {
          name: 'Ventilation Rating',
          value: 'Excellent',
          type: 'select',
          options: ['Poor', 'Good', 'Excellent']
        }
      ]
    });

    try {
      await testRoom.save();
      console.log('✅ Room created successfully with facilities');
      console.log('Room ID:', testRoom._id);
      console.log('Facilities:', testRoom.facilities);
      console.log('Custom Fields:', testRoom.customFields);
    } catch (error) {
      if (error.code === 11000) {
        console.log('⚠️  Room already exists (duplicate key)');
      } else {
        console.log('❌ Error creating room:', error.message);
      }
    }

    // Test 4: Create a user and teacher with personal info
    console.log('\n=== Testing Teacher Model ===');
    
    // First create a user
    const testUser = new User({
      username: 'testteacher',
      email: '<EMAIL>',
      password: 'password123',
      role: 'teacher',
      profile: {
        firstName: 'Test',
        lastName: 'Teacher',
        firstNameSinhala: 'පරීක්ෂණ',
        lastNameSinhala: 'ගුරුවරයා',
        phone: '+94771234567'
      }
    });

    let userId;
    try {
      await testUser.save();
      userId = testUser._id;
      console.log('✅ User created successfully');
    } catch (error) {
      if (error.code === 11000) {
        const existingUser = await User.findOne({ email: '<EMAIL>' });
        userId = existingUser._id;
        console.log('⚠️  User already exists, using existing user');
      } else {
        console.log('❌ Error creating user:', error.message);
        return;
      }
    }

    const testTeacher = new Teacher({
      employeeId: 'TEST001',
      user: userId,
      short: 'TD',
      fullName: 'Test Doctor',
      fullNameSinhala: 'පරීක්ෂණ වෛද්‍යවරයා',
      personalInfo: {
        title: 'Dr.',
        dateOfBirth: new Date('1980-03-15'),
        gender: 'female',
        nationality: 'Sri Lankan',
        nic: '198007501234',
        emergencyContact: {
          name: 'John Doe',
          relationship: 'Spouse',
          phone: '+94771234568'
        }
      },
      qualifications: [
        {
          degree: 'Ph.D. Chemistry',
          institution: 'University of Peradeniya',
          year: 2010,
          grade: 'Distinction'
        }
      ],
      experience: {
        totalYears: 12,
        currentSchoolYears: 5
      },
      color: '#FF5722',
      customFields: [
        {
          name: 'Research Area',
          value: 'Organic Chemistry',
          type: 'text'
        },
        {
          name: 'Publications Count',
          value: 15,
          type: 'number'
        },
        {
          name: 'Available for Research Supervision',
          value: true,
          type: 'boolean'
        }
      ],
      workingHours: {
        maxPeriodsPerDay: 6,
        maxPeriodsPerWeek: 30
      },
      specialRoles: ['department_head']
    });

    try {
      await testTeacher.save();
      console.log('✅ Teacher created successfully with personal info');
      console.log('Teacher ID:', testTeacher._id);
      console.log('Personal Info:', testTeacher.personalInfo);
      console.log('Custom Fields:', testTeacher.customFields);
    } catch (error) {
      if (error.code === 11000) {
        console.log('⚠️  Teacher already exists (duplicate key)');
      } else {
        console.log('❌ Error creating teacher:', error.message);
      }
    }

    console.log('\n=== All Tests Completed ===');
    console.log('✅ Enhanced models are working correctly!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await mongoose.connection.close();
    console.log('Database connection closed');
  }
};

// Run the test
if (require.main === module) {
  testEnhancedModels();
}

module.exports = { testEnhancedModels };
