const express = require('express');
const { body, validationResult, query } = require('express-validator');
const { Subject } = require('../models');
const { authenticated, adminOnly } = require('../middleware/auth');

const router = express.Router();

// Get all subjects
router.get('/', authenticated, [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('search').optional().trim(),
  query('grade').optional().isInt({ min: 1, max: 13 }),
  query('department').optional().isIn(['mathematics', 'science', 'languages', 'social_studies', 'arts', 'physical_education', 'technology', 'religion']),
  query('active').optional().isBoolean()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: 'Validation errors',
        messageSinhala: 'වලංගුකරණ දෝෂ',
        errors: errors.array()
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;

    // Build filter
    const filter = {};
    if (req.query.active !== undefined) {
      filter.isActive = req.query.active === 'true';
    }
    if (req.query.grade) {
      filter.grade = parseInt(req.query.grade);
    }
    if (req.query.department) {
      filter.department = req.query.department;
    }
    if (req.query.search) {
      const searchRegex = new RegExp(req.query.search, 'i');
      filter.$or = [
        { name: searchRegex },
        { nameSinhala: searchRegex },
        { code: searchRegex }
      ];
    }

    const subjects = await Subject.find(filter)
      .populate('prerequisites', 'name nameSinhala code')
      .sort({ grade: 1, name: 1 })
      .skip(skip)
      .limit(limit);

    const total = await Subject.countDocuments(filter);

    res.json({
      message: 'Subjects retrieved successfully',
      messageSinhala: 'විෂයයන් සාර්ථකව ලබා ගන්නා ලදී',
      subjects,
      pagination: {
        current: page,
        pages: Math.ceil(total / limit),
        total,
        limit
      }
    });
  } catch (error) {
    console.error('Get subjects error:', error);
    res.status(500).json({
      message: 'Failed to retrieve subjects',
      messageSinhala: 'විෂයයන් ලබා ගැනීමට අසමත් විය'
    });
  }
});

// Get subject by ID
router.get('/:id', authenticated, async (req, res) => {
  try {
    const subject = await Subject.findById(req.params.id)
      .populate('prerequisites', 'name nameSinhala code grade');

    if (!subject) {
      return res.status(404).json({
        message: 'Subject not found',
        messageSinhala: 'විෂයය සොයා ගත නොහැක'
      });
    }

    res.json({
      message: 'Subject retrieved successfully',
      messageSinhala: 'විෂයය සාර්ථකව ලබා ගන්නා ලදී',
      subject
    });
  } catch (error) {
    console.error('Get subject error:', error);
    res.status(500).json({
      message: 'Failed to retrieve subject',
      messageSinhala: 'විෂයය ලබා ගැනීමට අසමත් විය'
    });
  }
});

// Create new subject
router.post('/', adminOnly, [
  body('name').notEmpty().trim(),
  body('nameSinhala').notEmpty().trim(),
  body('short').optional().trim(),
  body('code').notEmpty().trim().toUpperCase(),
  body('department').isIn(['mathematics', 'science', 'languages', 'social_studies', 'arts', 'physical_education', 'technology', 'religion']),
  body('grade').isInt({ min: 1, max: 13 }),
  body('periodsPerWeek').isInt({ min: 1, max: 10 }),
  body('duration').optional().isInt({ min: 30, max: 80 }),
  body('color').optional().matches(/^#[0-9A-F]{6}$/i),
  body('customFields').optional().isArray(),
  body('customFields.*.name').optional().notEmpty().trim(),
  body('customFields.*.value').optional().exists(),
  body('customFields.*.type').optional().isIn(['text', 'number', 'boolean', 'date', 'select']),
  body('prerequisites').optional().isArray(),
  body('prerequisites.*').optional().isMongoId(),
  body('resources.requiresLab').optional().isBoolean(),
  body('resources.requiresComputer').optional().isBoolean(),
  body('resources.requiresProjector').optional().isBoolean(),
  body('preferences.preferredPeriods').optional().isArray(),
  body('preferences.avoidPeriods').optional().isArray(),
  body('preferences.consecutivePeriods').optional().isBoolean(),
  body('preferences.maxConsecutive').optional().isInt({ min: 1, max: 5 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: 'Validation errors',
        messageSinhala: 'වලංගුකරණ දෝෂ',
        errors: errors.array()
      });
    }

    // Check if subject code already exists
    const existingSubject = await Subject.findOne({ code: req.body.code });
    if (existingSubject) {
      return res.status(400).json({
        message: 'Subject code already exists',
        messageSinhala: 'විෂය කේතය දැනටමත් පවතී'
      });
    }

    const subject = new Subject(req.body);
    await subject.save();

    const populatedSubject = await Subject.findById(subject._id)
      .populate('prerequisites', 'name nameSinhala code grade');

    res.status(201).json({
      message: 'Subject created successfully',
      messageSinhala: 'විෂයය සාර්ථකව නිර්මාණය කරන ලදී',
      subject: populatedSubject
    });
  } catch (error) {
    console.error('Create subject error:', error);
    res.status(500).json({
      message: 'Failed to create subject',
      messageSinhala: 'විෂයය නිර්මාණය කිරීමට අසමත් විය'
    });
  }
});

// Update subject
router.put('/:id', adminOnly, [
  body('name').optional().notEmpty().trim(),
  body('nameSinhala').optional().notEmpty().trim(),
  body('code').optional().notEmpty().trim().toUpperCase(),
  body('department').optional().isIn(['mathematics', 'science', 'languages', 'social_studies', 'arts', 'physical_education', 'technology', 'religion']),
  body('grade').optional().isInt({ min: 1, max: 13 }),
  body('periodsPerWeek').optional().isInt({ min: 1, max: 10 }),
  body('duration').optional().isInt({ min: 30, max: 80 }),
  body('color').optional().matches(/^#[0-9A-F]{6}$/i),
  body('prerequisites').optional().isArray(),
  body('prerequisites.*').optional().isMongoId()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: 'Validation errors',
        messageSinhala: 'වලංගුකරණ දෝෂ',
        errors: errors.array()
      });
    }

    // Check if subject code is being changed and if it already exists
    if (req.body.code) {
      const existingSubject = await Subject.findOne({ 
        code: req.body.code,
        _id: { $ne: req.params.id }
      });
      if (existingSubject) {
        return res.status(400).json({
          message: 'Subject code already exists',
          messageSinhala: 'විෂය කේතය දැනටමත් පවතී'
        });
      }
    }

    const subject = await Subject.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    ).populate('prerequisites', 'name nameSinhala code grade');

    if (!subject) {
      return res.status(404).json({
        message: 'Subject not found',
        messageSinhala: 'විෂයය සොයා ගත නොහැක'
      });
    }

    res.json({
      message: 'Subject updated successfully',
      messageSinhala: 'විෂයය සාර්ථකව යාවත්කාලීන කරන ලදී',
      subject
    });
  } catch (error) {
    console.error('Update subject error:', error);
    res.status(500).json({
      message: 'Failed to update subject',
      messageSinhala: 'විෂයය යාවත්කාලීන කිරීමට අසමත් විය'
    });
  }
});

// Delete subject (soft delete)
router.delete('/:id', adminOnly, async (req, res) => {
  try {
    const subject = await Subject.findByIdAndUpdate(
      req.params.id,
      { isActive: false },
      { new: true }
    );

    if (!subject) {
      return res.status(404).json({
        message: 'Subject not found',
        messageSinhala: 'විෂයය සොයා ගත නොහැක'
      });
    }

    res.json({
      message: 'Subject deactivated successfully',
      messageSinhala: 'විෂයය සාර්ථකව අක්‍රිය කරන ලදී'
    });
  } catch (error) {
    console.error('Delete subject error:', error);
    res.status(500).json({
      message: 'Failed to deactivate subject',
      messageSinhala: 'විෂයය අක්‍රිය කිරීමට අසමත් විය'
    });
  }
});

// Get subjects by grade
router.get('/grade/:grade', authenticated, async (req, res) => {
  try {
    const grade = parseInt(req.params.grade);
    if (grade < 1 || grade > 13) {
      return res.status(400).json({
        message: 'Invalid grade',
        messageSinhala: 'වලංගු නොවන ශ්‍රේණිය'
      });
    }

    const subjects = await Subject.find({ 
      grade: grade,
      isActive: true 
    }).populate('prerequisites', 'name nameSinhala code')
      .sort({ name: 1 });

    res.json({
      message: 'Subjects retrieved successfully',
      messageSinhala: 'විෂයයන් සාර්ථකව ලබා ගන්නා ලදී',
      subjects
    });
  } catch (error) {
    console.error('Get subjects by grade error:', error);
    res.status(500).json({
      message: 'Failed to retrieve subjects',
      messageSinhala: 'විෂයයන් ලබා ගැනීමට අසමත් විය'
    });
  }
});

module.exports = router;
