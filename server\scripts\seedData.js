const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// Import models
const { User, Teacher, Subject, Class, Room, Timetable } = require('../models');

const seedData = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/school_timetable');
    console.log('Connected to MongoDB');

    // Clear existing data
    await User.deleteMany({});
    await Teacher.deleteMany({});
    await Subject.deleteMany({});
    await Class.deleteMany({});
    await Room.deleteMany({});
    await Timetable.deleteMany({});
    console.log('Cleared existing data');

    // Create admin user
    const adminUser = new User({
      username: 'admin',
      email: '<EMAIL>',
      password: 'admin123',
      role: 'admin',
      profile: {
        firstName: 'Admin',
        lastName: 'User',
        firstNameSinhala: 'පරිපාලක',
        lastNameSinhala: 'පරිශීලක'
      }
    });
    await adminUser.save();
    console.log('Created admin user');

    // Create teacher user
    const teacherUser = new User({
      username: 'teacher',
      email: '<EMAIL>',
      password: 'teacher123',
      role: 'teacher',
      profile: {
        firstName: 'John',
        lastName: 'Silva',
        firstNameSinhala: 'ජෝන්',
        lastNameSinhala: 'සිල්වා'
      }
    });
    await teacherUser.save();

    // Create more teacher users
    const teacherUsers = [];
    const teacherNames = [
      { first: 'Mary', last: 'Fernando', firstSi: 'මේරි', lastSi: 'ප්‍රනාන්දු' },
      { first: 'David', last: 'Perera', firstSi: 'ඩේවිඩ්', lastSi: 'පෙරේරා' },
      { first: 'Sarah', last: 'Jayawardena', firstSi: 'සාරා', lastSi: 'ජයවර්ධන' },
      { first: 'Michael', last: 'Rodrigo', firstSi: 'මයිකල්', lastSi: 'රොඩ්‍රිගෝ' },
      { first: 'Priya', last: 'Silva', firstSi: 'ප්‍රියා', lastSi: 'සිල්වා' },
      { first: 'Ravi', last: 'Mendis', firstSi: 'රවි', lastSi: 'මෙන්ඩිස්' },
      { first: 'Nimal', last: 'Gunasekara', firstSi: 'නිමල්', lastSi: 'ගුණසේකර' },
      { first: 'Kamala', last: 'Wickramasinghe', firstSi: 'කමලා', lastSi: 'වික්‍රමසිංහ' },
      { first: 'Sunil', last: 'Rajapaksa', firstSi: 'සුනිල්', lastSi: 'රාජපක්ෂ' },
      { first: 'Anura', last: 'Bandara', firstSi: 'අනුර', lastSi: 'බණ්ඩාර' },
      { first: 'Malini', last: 'Dissanayake', firstSi: 'මාලිනී', lastSi: 'දිසානායක' },
      { first: 'Rohan', last: 'Wijesinghe', firstSi: 'රෝහන්', lastSi: 'විජේසිංහ' },
      { first: 'Sandya', last: 'Karunaratne', firstSi: 'සන්ධ්‍යා', lastSi: 'කරුණාරත්න' },
      { first: 'Ajith', last: 'Senanayake', firstSi: 'අජිත්', lastSi: 'සේනානායක' },
      { first: 'Nilmini', last: 'Amarasinghe', firstSi: 'නිල්මිනී', lastSi: 'අමරසිංහ' }
    ];

    for (let i = 0; i < teacherNames.length; i++) {
      const user = new User({
        username: `teacher${i + 2}`,
        email: `teacher${i + 2}@school.lk`,
        password: 'teacher123',
        role: 'teacher',
        profile: {
          firstName: teacherNames[i].first,
          lastName: teacherNames[i].last,
          firstNameSinhala: teacherNames[i].firstSi,
          lastNameSinhala: teacherNames[i].lastSi
        }
      });
      await user.save();
      teacherUsers.push(user);
    }
    console.log('Created teacher users');

    // Create subjects
    const subjects = [
      // Mathematics Department
      { name: 'Mathematics', nameSinhala: 'ගණිතය', code: 'MATH', department: 'mathematics', grade: 10, periodsPerWeek: 6, duration: 40, isCore: true, color: '#FF5722', description: 'Core mathematics curriculum' },
      { name: 'Additional Mathematics', nameSinhala: 'අතිරේක ගණිතය', code: 'AMATH', department: 'mathematics', grade: 11, periodsPerWeek: 4, duration: 40, isCore: false, color: '#D32F2F', description: 'Advanced mathematics' },
      { name: 'Statistics', nameSinhala: 'සංඛ්‍යාන විද්‍යාව', code: 'STAT', department: 'mathematics', grade: 12, periodsPerWeek: 3, duration: 40, isCore: false, color: '#F44336', description: 'Statistical analysis' },

      // Science Department
      { name: 'Science', nameSinhala: 'විද්‍යාව', code: 'SCI', department: 'science', grade: 10, periodsPerWeek: 5, duration: 40, isCore: true, color: '#4CAF50', description: 'General science curriculum' },
      { name: 'Physics', nameSinhala: 'භෞතික විද්‍යාව', code: 'PHY', department: 'science', grade: 11, periodsPerWeek: 4, duration: 40, isCore: false, color: '#2E7D32', description: 'Physics fundamentals' },
      { name: 'Chemistry', nameSinhala: 'රසායන විද්‍යාව', code: 'CHEM', department: 'science', grade: 11, periodsPerWeek: 4, duration: 40, isCore: false, color: '#388E3C', description: 'Chemistry fundamentals' },
      { name: 'Biology', nameSinhala: 'ජීව විද්‍යාව', code: 'BIO', department: 'science', grade: 11, periodsPerWeek: 4, duration: 40, isCore: false, color: '#43A047', description: 'Biology fundamentals' },

      // Languages Department
      { name: 'Sinhala', nameSinhala: 'සිංහල', code: 'SIN', department: 'languages', grade: 10, periodsPerWeek: 4, duration: 40, isCore: true, color: '#2196F3', description: 'Sinhala language and literature' },
      { name: 'English', nameSinhala: 'ඉංග්‍රීසි', code: 'ENG', department: 'languages', grade: 10, periodsPerWeek: 4, duration: 40, isCore: true, color: '#9C27B0', description: 'English language and literature' },
      { name: 'Tamil', nameSinhala: 'දමිළ', code: 'TAM', department: 'languages', grade: 10, periodsPerWeek: 3, duration: 40, isCore: false, color: '#673AB7', description: 'Tamil language' },
      { name: 'French', nameSinhala: 'ප්‍රංශ', code: 'FR', department: 'languages', grade: 11, periodsPerWeek: 3, duration: 40, isCore: false, color: '#3F51B5', description: 'French language' },

      // Social Studies Department
      { name: 'History', nameSinhala: 'ඉතිහාසය', code: 'HIST', department: 'social_studies', grade: 10, periodsPerWeek: 3, duration: 40, isCore: true, color: '#FF9800', description: 'World and local history' },
      { name: 'Geography', nameSinhala: 'භූගෝල විද්‍යාව', code: 'GEO', department: 'social_studies', grade: 10, periodsPerWeek: 3, duration: 40, isCore: true, color: '#795548', description: 'Physical and human geography' },
      { name: 'Civics', nameSinhala: 'පුරවැසි අධ්‍යාපනය', code: 'CIV', department: 'social_studies', grade: 9, periodsPerWeek: 2, duration: 40, isCore: true, color: '#FF7043', description: 'Citizenship education' },
      { name: 'Economics', nameSinhala: 'ආර්ථික විද්‍යාව', code: 'ECON', department: 'social_studies', grade: 12, periodsPerWeek: 4, duration: 40, isCore: false, color: '#8D6E63', description: 'Economic principles' },

      // Arts Department
      { name: 'Art', nameSinhala: 'කලාව', code: 'ART', department: 'arts', grade: 10, periodsPerWeek: 2, duration: 40, isCore: false, color: '#E91E63', description: 'Visual arts and creativity' },
      { name: 'Music', nameSinhala: 'සංගීතය', code: 'MUS', department: 'arts', grade: 10, periodsPerWeek: 2, duration: 40, isCore: false, color: '#AD1457', description: 'Music theory and practice' },
      { name: 'Drama', nameSinhala: 'නාට්‍ය', code: 'DRA', department: 'arts', grade: 11, periodsPerWeek: 2, duration: 40, isCore: false, color: '#C2185B', description: 'Theatre and performance' },

      // Physical Education Department
      { name: 'Physical Education', nameSinhala: 'ශාරීරික අධ්‍යාපනය', code: 'PE', department: 'physical_education', grade: 10, periodsPerWeek: 2, duration: 40, isCore: true, color: '#607D8B', description: 'Physical fitness and sports' },
      { name: 'Health Education', nameSinhala: 'සෞඛ්‍ය අධ්‍යාපනය', code: 'HEALTH', department: 'physical_education', grade: 9, periodsPerWeek: 1, duration: 40, isCore: true, color: '#546E7A', description: 'Health and wellness' },

      // Technology Department
      { name: 'Information Technology', nameSinhala: 'තොරතුරු තාක්ෂණය', code: 'IT', department: 'technology', grade: 10, periodsPerWeek: 3, duration: 40, isCore: false, color: '#00BCD4', description: 'Computer skills and programming' },
      { name: 'Design & Technology', nameSinhala: 'නිර්මාණ හා තාක්ෂණය', code: 'DT', department: 'technology', grade: 11, periodsPerWeek: 3, duration: 40, isCore: false, color: '#0097A7', description: 'Design thinking and technology' },

      // Religion Department
      { name: 'Buddhism', nameSinhala: 'බුද්ධ ධර්මය', code: 'BUD', department: 'religion', grade: 10, periodsPerWeek: 2, duration: 40, isCore: true, color: '#FFC107', description: 'Buddhist philosophy and ethics' },
      { name: 'Christianity', nameSinhala: 'ක්‍රිස්තියානි ධර්මය', code: 'CHR', department: 'religion', grade: 10, periodsPerWeek: 2, duration: 40, isCore: false, color: '#FFB300', description: 'Christian studies' },
      { name: 'Islam', nameSinhala: 'ඉස්ලාම් ධර්මය', code: 'ISL', department: 'religion', grade: 10, periodsPerWeek: 2, duration: 40, isCore: false, color: '#FF8F00', description: 'Islamic studies' },
      { name: 'Hinduism', nameSinhala: 'හින්දු ධර්මය', code: 'HIN', department: 'religion', grade: 10, periodsPerWeek: 2, duration: 40, isCore: false, color: '#FF6F00', description: 'Hindu philosophy' }
    ];

    const createdSubjects = [];
    for (const subjectData of subjects) {
      const subject = new Subject(subjectData);
      await subject.save();
      createdSubjects.push(subject);
    }
    console.log('Created subjects');

    // Create rooms
    const rooms = [
      // Main Building - Classrooms
      { number: '101', name: 'Classroom 101', nameSinhala: 'පන්ති කාමරය 101', type: 'classroom', building: 'Main Building', floor: 1, capacity: 35, hasProjector: true, hasAC: false },
      { number: '102', name: 'Classroom 102', nameSinhala: 'පන්ති කාමරය 102', type: 'classroom', building: 'Main Building', floor: 1, capacity: 35, hasProjector: true, hasAC: false },
      { number: '103', name: 'Classroom 103', nameSinhala: 'පන්ති කාමරය 103', type: 'classroom', building: 'Main Building', floor: 1, capacity: 35, hasProjector: false, hasAC: false },
      { number: '104', name: 'Classroom 104', nameSinhala: 'පන්ති කාමරය 104', type: 'classroom', building: 'Main Building', floor: 1, capacity: 35, hasProjector: true, hasAC: true },
      { number: '201', name: 'Classroom 201', nameSinhala: 'පන්ති කාමරය 201', type: 'classroom', building: 'Main Building', floor: 2, capacity: 40, hasProjector: true, hasAC: true },
      { number: '202', name: 'Classroom 202', nameSinhala: 'පන්ති කාමරය 202', type: 'classroom', building: 'Main Building', floor: 2, capacity: 40, hasProjector: true, hasAC: true },
      { number: '203', name: 'Classroom 203', nameSinhala: 'පන්ති කාමරය 203', type: 'classroom', building: 'Main Building', floor: 2, capacity: 40, hasProjector: false, hasAC: false },

      // Science Block - Laboratories
      { number: 'SL01', name: 'Physics Laboratory', nameSinhala: 'භෞතික විද්‍යා රසායනාගාරය', type: 'laboratory', building: 'Science Block', floor: 1, capacity: 30, hasProjector: true, hasAC: true },
      { number: 'SL02', name: 'Chemistry Laboratory', nameSinhala: 'රසායන විද්‍යා රසායනාගාරය', type: 'laboratory', building: 'Science Block', floor: 1, capacity: 30, hasProjector: true, hasAC: true },
      { number: 'SL03', name: 'Biology Laboratory', nameSinhala: 'ජීව විද්‍යා රසායනාගාරය', type: 'laboratory', building: 'Science Block', floor: 2, capacity: 30, hasProjector: true, hasAC: true },
      { number: 'SL04', name: 'General Science Lab', nameSinhala: 'සාමාන්‍ය විද්‍යා රසායනාගාරය', type: 'laboratory', building: 'Science Block', floor: 2, capacity: 35, hasProjector: true, hasAC: false },

      // Technology Block
      { number: 'CL01', name: 'Computer Lab 1', nameSinhala: 'පරිගණක රසායනාගාරය 1', type: 'computer_lab', building: 'Technology Block', floor: 1, capacity: 25, hasProjector: true, hasAC: true },
      { number: 'CL02', name: 'Computer Lab 2', nameSinhala: 'පරිගණක රසායනාගාරය 2', type: 'computer_lab', building: 'Technology Block', floor: 1, capacity: 25, hasProjector: true, hasAC: true },
      { number: 'DT01', name: 'Design & Technology Workshop', nameSinhala: 'නිර්මාණ හා තාක්ෂණ වැඩමුළුව', type: 'classroom', building: 'Technology Block', floor: 2, capacity: 20, hasProjector: false, hasAC: false },

      // Arts Block
      { number: 'AR01', name: 'Art Studio', nameSinhala: 'කලා චිත්‍රාගාරය', type: 'art_room', building: 'Arts Block', floor: 1, capacity: 20, hasProjector: false, hasAC: false },
      { number: 'MU01', name: 'Music Room', nameSinhala: 'සංගීත කාමරය', type: 'music_room', building: 'Arts Block', floor: 1, capacity: 25, hasProjector: false, hasAC: true },
      { number: 'DR01', name: 'Drama Hall', nameSinhala: 'නාට්‍ය ශාලාව', type: 'auditorium', building: 'Arts Block', floor: 2, capacity: 80, hasProjector: true, hasAC: true },

      // Sports Complex
      { number: 'GYM', name: 'Gymnasium', nameSinhala: 'ව්‍යායාම ශාලාව', type: 'gymnasium', building: 'Sports Complex', floor: 0, capacity: 50, hasProjector: false, hasAC: false },
      { number: 'POOL', name: 'Swimming Pool', nameSinhala: 'පිහිනුම් තටාකය', type: 'gymnasium', building: 'Sports Complex', floor: 0, capacity: 30, hasProjector: false, hasAC: false },

      // Special Rooms
      { number: 'LIB', name: 'Library', nameSinhala: 'පුස්තකාලය', type: 'library', building: 'Main Building', floor: 3, capacity: 60, hasProjector: true, hasAC: true },
      { number: 'AUD', name: 'Main Auditorium', nameSinhala: 'ප්‍රධාන ශ්‍රවණාගාරය', type: 'auditorium', building: 'Main Building', floor: 0, capacity: 100, hasProjector: true, hasAC: true },
      { number: 'CONF', name: 'Conference Room', nameSinhala: 'සම්මන්ත්‍රණ කාමරය', type: 'office', building: 'Administration Block', floor: 2, capacity: 15, hasProjector: true, hasAC: true },
      { number: 'STAFF', name: 'Staff Room', nameSinhala: 'කාර්ය මණ්ඩල කාමරය', type: 'staff_room', building: 'Administration Block', floor: 1, capacity: 25, hasProjector: false, hasAC: true }
    ];

    const createdRooms = [];
    for (const roomData of rooms) {
      const room = new Room(roomData);
      await room.save();
      createdRooms.push(room);
    }
    console.log('Created rooms');

    // Create teachers
    const teachers = [
      // Mathematics Department
      { user: teacherUser._id, employeeId: 'T001', subjects: [createdSubjects[0]._id, createdSubjects[1]._id, createdSubjects[2]._id], isClassTeacher: true }, // Math, Additional Math, Statistics

      // Science Department
      { user: teacherUsers[0]._id, employeeId: 'T002', subjects: [createdSubjects[3]._id, createdSubjects[4]._id], isClassTeacher: true }, // Science, Physics
      { user: teacherUsers[1]._id, employeeId: 'T003', subjects: [createdSubjects[5]._id, createdSubjects[6]._id] }, // Chemistry, Biology

      // Languages Department
      { user: teacherUsers[2]._id, employeeId: 'T004', subjects: [createdSubjects[7]._id] }, // Sinhala
      { user: teacherUsers[3]._id, employeeId: 'T005', subjects: [createdSubjects[8]._id] }, // English
      { user: teacherUsers[4]._id, employeeId: 'T006', subjects: [createdSubjects[9]._id, createdSubjects[10]._id] }, // Tamil, French

      // Social Studies Department
      { user: teacherUsers[5]._id, employeeId: 'T007', subjects: [createdSubjects[11]._id, createdSubjects[12]._id] }, // History, Geography
      { user: teacherUsers[6]._id, employeeId: 'T008', subjects: [createdSubjects[13]._id, createdSubjects[14]._id] }, // Civics, Economics

      // Arts Department
      { user: teacherUsers[7]._id, employeeId: 'T009', subjects: [createdSubjects[15]._id] }, // Art
      { user: teacherUsers[8]._id, employeeId: 'T010', subjects: [createdSubjects[16]._id, createdSubjects[17]._id] }, // Music, Drama

      // Physical Education Department
      { user: teacherUsers[9]._id, employeeId: 'T011', subjects: [createdSubjects[18]._id, createdSubjects[19]._id] }, // PE, Health Education

      // Technology Department
      { user: teacherUsers[10]._id, employeeId: 'T012', subjects: [createdSubjects[20]._id, createdSubjects[21]._id] }, // IT, Design & Technology

      // Religion Department
      { user: teacherUsers[11]._id, employeeId: 'T013', subjects: [createdSubjects[22]._id] }, // Buddhism
      { user: teacherUsers[12]._id, employeeId: 'T014', subjects: [createdSubjects[23]._id, createdSubjects[24]._id] }, // Christianity, Islam
      { user: teacherUsers[13]._id, employeeId: 'T015', subjects: [createdSubjects[25]._id] }, // Hinduism

      // Additional teachers for coverage
      { user: teacherUsers[14]._id, employeeId: 'T016', subjects: [createdSubjects[0]._id, createdSubjects[3]._id] } // Math & Science support
    ];

    const createdTeachers = [];
    for (const teacherData of teachers) {
      const teacher = new Teacher(teacherData);
      await teacher.save();
      createdTeachers.push(teacher);
    }
    console.log('Created teachers');

    // Create classes
    const classes = [
      {
        name: '9A',
        nameSinhala: '9 අ',
        grade: 9,
        section: 'A',
        capacity: 35,
        classTeacher: createdTeachers[0]._id,
        room: createdRooms[0]._id, // Classroom 101
        subjects: [
          { subject: createdSubjects[0]._id, teacher: createdTeachers[0]._id, periodsPerWeek: 6 }, // Mathematics
          { subject: createdSubjects[3]._id, teacher: createdTeachers[1]._id, periodsPerWeek: 5 }, // Science
          { subject: createdSubjects[7]._id, teacher: createdTeachers[3]._id, periodsPerWeek: 4 }, // Sinhala
          { subject: createdSubjects[8]._id, teacher: createdTeachers[4]._id, periodsPerWeek: 4 }, // English
          { subject: createdSubjects[11]._id, teacher: createdTeachers[6]._id, periodsPerWeek: 3 }, // History
          { subject: createdSubjects[12]._id, teacher: createdTeachers[6]._id, periodsPerWeek: 3 }, // Geography
          { subject: createdSubjects[13]._id, teacher: createdTeachers[7]._id, periodsPerWeek: 2 }, // Civics
          { subject: createdSubjects[15]._id, teacher: createdTeachers[8]._id, periodsPerWeek: 2 }, // Art
          { subject: createdSubjects[18]._id, teacher: createdTeachers[10]._id, periodsPerWeek: 2 }, // PE
          { subject: createdSubjects[19]._id, teacher: createdTeachers[10]._id, periodsPerWeek: 1 }, // Health Education
          { subject: createdSubjects[22]._id, teacher: createdTeachers[12]._id, periodsPerWeek: 2 } // Buddhism
        ]
      },
      {
        name: '9B',
        nameSinhala: '9 ආ',
        grade: 9,
        section: 'B',
        capacity: 35,
        classTeacher: createdTeachers[1]._id,
        room: createdRooms[1]._id, // Classroom 102
        subjects: [
          { subject: createdSubjects[0]._id, teacher: createdTeachers[15]._id, periodsPerWeek: 6 }, // Mathematics
          { subject: createdSubjects[3]._id, teacher: createdTeachers[15]._id, periodsPerWeek: 5 }, // Science
          { subject: createdSubjects[7]._id, teacher: createdTeachers[3]._id, periodsPerWeek: 4 }, // Sinhala
          { subject: createdSubjects[8]._id, teacher: createdTeachers[4]._id, periodsPerWeek: 4 }, // English
          { subject: createdSubjects[11]._id, teacher: createdTeachers[6]._id, periodsPerWeek: 3 }, // History
          { subject: createdSubjects[12]._id, teacher: createdTeachers[6]._id, periodsPerWeek: 3 }, // Geography
          { subject: createdSubjects[13]._id, teacher: createdTeachers[7]._id, periodsPerWeek: 2 }, // Civics
          { subject: createdSubjects[16]._id, teacher: createdTeachers[9]._id, periodsPerWeek: 2 }, // Music
          { subject: createdSubjects[18]._id, teacher: createdTeachers[10]._id, periodsPerWeek: 2 }, // PE
          { subject: createdSubjects[19]._id, teacher: createdTeachers[10]._id, periodsPerWeek: 1 }, // Health Education
          { subject: createdSubjects[22]._id, teacher: createdTeachers[12]._id, periodsPerWeek: 2 } // Buddhism
        ]
      },
      {
        name: '10A',
        nameSinhala: '10 අ',
        grade: 10,
        section: 'A',
        capacity: 35,
        classTeacher: createdTeachers[2]._id,
        room: createdRooms[2]._id, // Classroom 103
        subjects: [
          { subject: createdSubjects[0]._id, teacher: createdTeachers[0]._id, periodsPerWeek: 6 }, // Mathematics
          { subject: createdSubjects[3]._id, teacher: createdTeachers[1]._id, periodsPerWeek: 5 }, // Science
          { subject: createdSubjects[7]._id, teacher: createdTeachers[3]._id, periodsPerWeek: 4 }, // Sinhala
          { subject: createdSubjects[8]._id, teacher: createdTeachers[4]._id, periodsPerWeek: 4 }, // English
          { subject: createdSubjects[11]._id, teacher: createdTeachers[6]._id, periodsPerWeek: 3 }, // History
          { subject: createdSubjects[12]._id, teacher: createdTeachers[6]._id, periodsPerWeek: 3 }, // Geography
          { subject: createdSubjects[15]._id, teacher: createdTeachers[8]._id, periodsPerWeek: 2 }, // Art
          { subject: createdSubjects[18]._id, teacher: createdTeachers[10]._id, periodsPerWeek: 2 }, // PE
          { subject: createdSubjects[20]._id, teacher: createdTeachers[11]._id, periodsPerWeek: 3 }, // IT
          { subject: createdSubjects[22]._id, teacher: createdTeachers[12]._id, periodsPerWeek: 2 } // Buddhism
        ]
      },
      {
        name: '10B',
        nameSinhala: '10 ආ',
        grade: 10,
        section: 'B',
        capacity: 35,
        classTeacher: createdTeachers[3]._id,
        room: createdRooms[3]._id, // Classroom 104
        subjects: [
          { subject: createdSubjects[0]._id, teacher: createdTeachers[15]._id, periodsPerWeek: 6 }, // Mathematics
          { subject: createdSubjects[3]._id, teacher: createdTeachers[2]._id, periodsPerWeek: 5 }, // Science
          { subject: createdSubjects[7]._id, teacher: createdTeachers[3]._id, periodsPerWeek: 4 }, // Sinhala
          { subject: createdSubjects[8]._id, teacher: createdTeachers[4]._id, periodsPerWeek: 4 }, // English
          { subject: createdSubjects[11]._id, teacher: createdTeachers[6]._id, periodsPerWeek: 3 }, // History
          { subject: createdSubjects[12]._id, teacher: createdTeachers[6]._id, periodsPerWeek: 3 }, // Geography
          { subject: createdSubjects[16]._id, teacher: createdTeachers[9]._id, periodsPerWeek: 2 }, // Music
          { subject: createdSubjects[18]._id, teacher: createdTeachers[10]._id, periodsPerWeek: 2 }, // PE
          { subject: createdSubjects[20]._id, teacher: createdTeachers[11]._id, periodsPerWeek: 3 }, // IT
          { subject: createdSubjects[22]._id, teacher: createdTeachers[12]._id, periodsPerWeek: 2 } // Buddhism
        ]
      },
      {
        name: '11A',
        nameSinhala: '11 අ',
        grade: 11,
        section: 'A',
        capacity: 40,
        classTeacher: createdTeachers[4]._id,
        room: createdRooms[4]._id, // Classroom 201
        subjects: [
          { subject: createdSubjects[1]._id, teacher: createdTeachers[0]._id, periodsPerWeek: 4 }, // Additional Mathematics
          { subject: createdSubjects[4]._id, teacher: createdTeachers[1]._id, periodsPerWeek: 4 }, // Physics
          { subject: createdSubjects[5]._id, teacher: createdTeachers[2]._id, periodsPerWeek: 4 }, // Chemistry
          { subject: createdSubjects[6]._id, teacher: createdTeachers[2]._id, periodsPerWeek: 4 }, // Biology
          { subject: createdSubjects[8]._id, teacher: createdTeachers[4]._id, periodsPerWeek: 4 }, // English
          { subject: createdSubjects[10]._id, teacher: createdTeachers[5]._id, periodsPerWeek: 3 }, // French
          { subject: createdSubjects[17]._id, teacher: createdTeachers[9]._id, periodsPerWeek: 2 }, // Drama
          { subject: createdSubjects[21]._id, teacher: createdTeachers[11]._id, periodsPerWeek: 3 }, // Design & Technology
          { subject: createdSubjects[22]._id, teacher: createdTeachers[12]._id, periodsPerWeek: 2 } // Buddhism
        ]
      },
      {
        name: '12A',
        nameSinhala: '12 අ',
        grade: 12,
        section: 'A',
        capacity: 40,
        classTeacher: createdTeachers[5]._id,
        room: createdRooms[5]._id, // Classroom 202
        subjects: [
          { subject: createdSubjects[2]._id, teacher: createdTeachers[0]._id, periodsPerWeek: 3 }, // Statistics
          { subject: createdSubjects[4]._id, teacher: createdTeachers[1]._id, periodsPerWeek: 4 }, // Physics
          { subject: createdSubjects[5]._id, teacher: createdTeachers[2]._id, periodsPerWeek: 4 }, // Chemistry
          { subject: createdSubjects[8]._id, teacher: createdTeachers[4]._id, periodsPerWeek: 4 }, // English
          { subject: createdSubjects[14]._id, teacher: createdTeachers[7]._id, periodsPerWeek: 4 }, // Economics
          { subject: createdSubjects[21]._id, teacher: createdTeachers[11]._id, periodsPerWeek: 3 }, // Design & Technology
          { subject: createdSubjects[22]._id, teacher: createdTeachers[12]._id, periodsPerWeek: 2 } // Buddhism
        ]
      }
    ];

    const createdClasses = [];
    for (const classData of classes) {
      const classObj = new Class(classData);
      await classObj.save();
      createdClasses.push(classObj);
    }
    console.log('Created classes');

    // Update teachers with class assignments
    createdTeachers[0].isClassTeacher = true;
    createdTeachers[0].classTeacherOf = createdClasses[0]._id;
    createdTeachers[0].classes = [createdClasses[0]._id, createdClasses[1]._id];
    await createdTeachers[0].save();

    createdTeachers[1].isClassTeacher = true;
    createdTeachers[1].classTeacherOf = createdClasses[1]._id;
    createdTeachers[1].classes = [createdClasses[0]._id, createdClasses[1]._id];
    await createdTeachers[1].save();

    for (let i = 2; i < createdTeachers.length; i++) {
      createdTeachers[i].classes = [createdClasses[0]._id, createdClasses[1]._id];
      await createdTeachers[i].save();
    }

    console.log('Sample data seeded successfully!');
    console.log('\nLogin credentials:');
    console.log('Admin: <EMAIL> / admin123');
    console.log('Teacher: <EMAIL> / teacher123');
    
    process.exit(0);
  } catch (error) {
    console.error('Error seeding data:', error);
    process.exit(1);
  }
};

// Run the seeder
seedData();
