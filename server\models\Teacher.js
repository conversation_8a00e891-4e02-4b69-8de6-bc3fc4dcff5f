const mongoose = require('mongoose');

// Custom field schema for flexible teacher properties
const customFieldSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  value: {
    type: mongoose.Schema.Types.Mixed,
    required: true
  },
  type: {
    type: String,
    enum: ['text', 'number', 'boolean', 'date', 'select'],
    default: 'text'
  },
  options: [String] // For select type fields
}, { _id: false });

const teacherSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  employeeId: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  // Personal Information
  personalInfo: {
    title: {
      type: String,
      enum: ['Mr.', 'Mrs.', 'Miss', 'Dr.', 'Prof.'],
      default: 'Mr.'
    },
    dateOfBirth: Date,
    gender: {
      type: String,
      enum: ['male', 'female', 'other']
    },
    nationality: {
      type: String,
      default: 'Sri Lankan'
    },
    nic: {
      type: String,
      trim: true
    },
    emergencyContact: {
      name: String,
      relationship: String,
      phone: String
    }
  },
  subjects: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Subject'
  }],
  classes: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Class'
  }],
  qualifications: [{
    degree: String,
    institution: String,
    year: Number,
    grade: String
  }],
  experience: {
    totalYears: {
      type: Number,
      default: 0
    },
    currentSchoolYears: {
      type: Number,
      default: 0
    },
    previousSchools: [{
      name: String,
      position: String,
      startDate: Date,
      endDate: Date
    }]
  },
  // Color for visual identification in timetables
  color: {
    type: String,
    default: '#FF9800', // Material Design Orange
    match: /^#[0-9A-F]{6}$/i
  },
  // Custom fields for additional teacher properties
  customFields: [customFieldSchema],
  workingHours: {
    maxPeriodsPerDay: {
      type: Number,
      default: 6
    },
    maxPeriodsPerWeek: {
      type: Number,
      default: 30
    },
    preferredPeriods: [{
      day: {
        type: String,
        enum: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday']
      },
      periods: [Number]
    }],
    unavailablePeriods: [{
      day: {
        type: String,
        enum: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday']
      },
      periods: [Number],
      reason: String
    }]
  },
  isClassTeacher: {
    type: Boolean,
    default: false
  },
  classTeacherOf: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Class'
  },
  specialRoles: [{
    type: String,
    enum: ['principal', 'vice_principal', 'department_head', 'coordinator', 'librarian', 'counselor']
  }],
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Indexes for better performance
teacherSchema.index({ employeeId: 1 });
teacherSchema.index({ user: 1 });
teacherSchema.index({ subjects: 1 });
teacherSchema.index({ classes: 1 });

module.exports = mongoose.model('Teacher', teacherSchema);
