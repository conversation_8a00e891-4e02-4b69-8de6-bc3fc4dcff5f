const express = require('express');
const { body, validationResult, query } = require('express-validator');
const { Room } = require('../models');
const { authenticated, adminOnly } = require('../middleware/auth');

const router = express.Router();

// Get all rooms
router.get('/', authenticated, [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('search').optional().trim(),
  query('type').optional().isIn(['classroom', 'laboratory', 'computer_lab', 'library', 'auditorium', 'gymnasium', 'art_room', 'music_room', 'staff_room', 'office']),
  query('building').optional().trim(),
  query('floor').optional().isInt({ min: 0, max: 10 }),
  query('active').optional().isBoolean()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: 'Validation errors',
        messageSinhala: 'වලංගුකරණ දෝෂ',
        errors: errors.array()
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;

    // Build filter
    const filter = {};
    if (req.query.active !== undefined) {
      filter.isActive = req.query.active === 'true';
    }
    if (req.query.type) {
      filter.type = req.query.type;
    }
    if (req.query.building) {
      filter.building = new RegExp(req.query.building, 'i');
    }
    if (req.query.floor !== undefined) {
      filter.floor = parseInt(req.query.floor);
    }
    if (req.query.search) {
      const searchRegex = new RegExp(req.query.search, 'i');
      filter.$or = [
        { number: searchRegex },
        { name: searchRegex },
        { nameSinhala: searchRegex },
        { building: searchRegex }
      ];
    }

    const rooms = await Room.find(filter)
      .populate('subjects', 'name nameSinhala code')
      .populate('assignedClass', 'name nameSinhala grade section')
      .sort({ building: 1, floor: 1, number: 1 })
      .skip(skip)
      .limit(limit);

    const total = await Room.countDocuments(filter);

    res.json({
      message: 'Rooms retrieved successfully',
      messageSinhala: 'කාමර සාර්ථකව ලබා ගන්නා ලදී',
      rooms,
      pagination: {
        current: page,
        pages: Math.ceil(total / limit),
        total,
        limit
      }
    });
  } catch (error) {
    console.error('Get rooms error:', error);
    res.status(500).json({
      message: 'Failed to retrieve rooms',
      messageSinhala: 'කාමර ලබා ගැනීමට අසමත් විය'
    });
  }
});

// Get room by ID
router.get('/:id', authenticated, async (req, res) => {
  try {
    const room = await Room.findById(req.params.id)
      .populate('subjects', 'name nameSinhala code department')
      .populate('assignedClass', 'name nameSinhala grade section capacity students');

    if (!room) {
      return res.status(404).json({
        message: 'Room not found',
        messageSinhala: 'කාමරය සොයා ගත නොහැක'
      });
    }

    res.json({
      message: 'Room retrieved successfully',
      messageSinhala: 'කාමරය සාර්ථකව ලබා ගන්නා ලදී',
      room
    });
  } catch (error) {
    console.error('Get room error:', error);
    res.status(500).json({
      message: 'Failed to retrieve room',
      messageSinhala: 'කාමරය ලබා ගැනීමට අසමත් විය'
    });
  }
});

// Create new room
router.post('/', adminOnly, [
  body('classroomName').notEmpty().trim(),
  body('number').notEmpty().trim(),
  body('name').notEmpty().trim(),
  body('nameSinhala').optional().trim(),
  body('short').optional().trim(),
  body('type').isIn(['classroom', 'laboratory', 'computer_lab', 'library', 'auditorium', 'gymnasium', 'art_room', 'music_room', 'staff_room', 'office']),
  body('nameClassroom').optional().isBoolean(),
  body('sharedRoom').optional().isBoolean(),
  body('mayBeClassroom').optional().isBoolean(),
  body('building').notEmpty().trim(),
  body('floor').isInt({ min: 0, max: 10 }),
  body('capacity').isInt({ min: 1, max: 100 }),
  body('color').optional().matches(/^#[0-9A-F]{6}$/i),
  body('customFields').optional().isArray(),
  body('customFields.*.name').optional().notEmpty().trim(),
  body('customFields.*.value').optional().exists(),
  body('customFields.*.type').optional().isIn(['text', 'number', 'boolean', 'date', 'select']),
  body('subjects').optional().isArray(),
  body('subjects.*').optional().isMongoId(),
  body('assignedClass').optional().isMongoId(),
  body('facilities.hasProjector').optional().isBoolean(),
  body('facilities.hasComputers').optional().isBoolean(),
  body('facilities.computerCount').optional().isInt({ min: 0 }),
  body('facilities.hasAirConditioning').optional().isBoolean(),
  body('facilities.hasWhiteboard').optional().isBoolean(),
  body('facilities.hasSmartBoard').optional().isBoolean(),
  body('facilities.hasAudioSystem').optional().isBoolean()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: 'Validation errors',
        messageSinhala: 'වලංගුකරණ දෝෂ',
        errors: errors.array()
      });
    }

    // Check if room number already exists
    const existingRoom = await Room.findOne({ number: req.body.number });
    if (existingRoom) {
      return res.status(400).json({
        message: 'Room number already exists',
        messageSinhala: 'කාමර අංකය දැනටමත් පවතී'
      });
    }

    const room = new Room(req.body);
    await room.save();

    const populatedRoom = await Room.findById(room._id)
      .populate('subjects', 'name nameSinhala code')
      .populate('assignedClass', 'name nameSinhala grade section');

    res.status(201).json({
      message: 'Room created successfully',
      messageSinhala: 'කාමරය සාර්ථකව නිර්මාණය කරන ලදී',
      room: populatedRoom
    });
  } catch (error) {
    console.error('Create room error:', error);
    res.status(500).json({
      message: 'Failed to create room',
      messageSinhala: 'කාමරය නිර්මාණය කිරීමට අසමත් විය'
    });
  }
});

// Update room
router.put('/:id', adminOnly, [
  body('number').optional().notEmpty().trim(),
  body('name').optional().notEmpty().trim(),
  body('nameSinhala').optional().trim(),
  body('type').optional().isIn(['classroom', 'laboratory', 'computer_lab', 'library', 'auditorium', 'gymnasium', 'art_room', 'music_room', 'staff_room', 'office']),
  body('building').optional().notEmpty().trim(),
  body('floor').optional().isInt({ min: 0, max: 10 }),
  body('capacity').optional().isInt({ min: 1, max: 100 }),
  body('subjects').optional().isArray(),
  body('subjects.*').optional().isMongoId(),
  body('assignedClass').optional().isMongoId()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: 'Validation errors',
        messageSinhala: 'වලංගුකරණ දෝෂ',
        errors: errors.array()
      });
    }

    // Check if room number is being changed and if it already exists
    if (req.body.number) {
      const existingRoom = await Room.findOne({ 
        number: req.body.number,
        _id: { $ne: req.params.id }
      });
      if (existingRoom) {
        return res.status(400).json({
          message: 'Room number already exists',
          messageSinhala: 'කාමර අංකය දැනටමත් පවතී'
        });
      }
    }

    const room = await Room.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    ).populate('subjects', 'name nameSinhala code')
     .populate('assignedClass', 'name nameSinhala grade section');

    if (!room) {
      return res.status(404).json({
        message: 'Room not found',
        messageSinhala: 'කාමරය සොයා ගත නොහැක'
      });
    }

    res.json({
      message: 'Room updated successfully',
      messageSinhala: 'කාමරය සාර්ථකව යාවත්කාලීන කරන ලදී',
      room
    });
  } catch (error) {
    console.error('Update room error:', error);
    res.status(500).json({
      message: 'Failed to update room',
      messageSinhala: 'කාමරය යාවත්කාලීන කිරීමට අසමත් විය'
    });
  }
});

// Delete room (soft delete)
router.delete('/:id', adminOnly, async (req, res) => {
  try {
    const room = await Room.findByIdAndUpdate(
      req.params.id,
      { isActive: false },
      { new: true }
    );

    if (!room) {
      return res.status(404).json({
        message: 'Room not found',
        messageSinhala: 'කාමරය සොයා ගත නොහැක'
      });
    }

    res.json({
      message: 'Room deactivated successfully',
      messageSinhala: 'කාමරය සාර්ථකව අක්‍රිය කරන ලදී'
    });
  } catch (error) {
    console.error('Delete room error:', error);
    res.status(500).json({
      message: 'Failed to deactivate room',
      messageSinhala: 'කාමරය අක්‍රිය කිරීමට අසමත් විය'
    });
  }
});

// Check room availability
router.get('/:id/availability', authenticated, [
  query('date').optional().isISO8601(),
  query('period').optional().isInt({ min: 1, max: 10 })
], async (req, res) => {
  try {
    const room = await Room.findById(req.params.id);
    if (!room) {
      return res.status(404).json({
        message: 'Room not found',
        messageSinhala: 'කාමරය සොයා ගත නොහැක'
      });
    }

    // This is a simplified availability check
    // In a real implementation, you'd check against the timetable
    const availability = {
      monday: Array.from({length: 8}, (_, i) => ({ period: i + 1, isAvailable: true })),
      tuesday: Array.from({length: 8}, (_, i) => ({ period: i + 1, isAvailable: true })),
      wednesday: Array.from({length: 8}, (_, i) => ({ period: i + 1, isAvailable: true })),
      thursday: Array.from({length: 8}, (_, i) => ({ period: i + 1, isAvailable: true })),
      friday: Array.from({length: 8}, (_, i) => ({ period: i + 1, isAvailable: true })),
      saturday: Array.from({length: 6}, (_, i) => ({ period: i + 1, isAvailable: true }))
    };

    res.json({
      message: 'Room availability retrieved successfully',
      messageSinhala: 'කාමර ලබා ගැනීමේ හැකියාව සාර්ථකව ලබා ගන්නා ලදී',
      room: {
        id: room._id,
        number: room.number,
        name: room.displayName
      },
      availability
    });
  } catch (error) {
    console.error('Get room availability error:', error);
    res.status(500).json({
      message: 'Failed to retrieve room availability',
      messageSinhala: 'කාමර ලබා ගැනීමේ හැකියාව ලබා ගැනීමට අසමත් විය'
    });
  }
});

module.exports = router;
