const express = require('express');
const { body, validationResult, query } = require('express-validator');
const { Teacher, User, Subject, Class } = require('../models');
const { authenticated, adminOnly, adminOrTeacher } = require('../middleware/auth');

const router = express.Router();

// Get all teachers
router.get('/', authenticated, [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('search').optional().trim(),
  query('subject').optional().isMongoId(),
  query('active').optional().isBoolean()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: 'Validation errors',
        messageSinhala: 'වලංගුකරණ දෝෂ',
        errors: errors.array()
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Build filter
    const filter = {};
    if (req.query.active !== undefined) {
      filter.isActive = req.query.active === 'true';
    }
    if (req.query.subject) {
      filter.subjects = req.query.subject;
    }

    // Build query
    let query = Teacher.find(filter)
      .populate('user', 'profile preferences')
      .populate('subjects', 'name nameSinhala code')
      .populate('classes', 'name nameSinhala grade section')
      .populate('classTeacherOf', 'name nameSinhala grade section')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    // Add search if provided
    if (req.query.search) {
      const searchRegex = new RegExp(req.query.search, 'i');
      const users = await User.find({
        $or: [
          { 'profile.firstName': searchRegex },
          { 'profile.lastName': searchRegex },
          { 'profile.firstNameSinhala': searchRegex },
          { 'profile.lastNameSinhala': searchRegex }
        ]
      }).select('_id');
      
      filter.$or = [
        { employeeId: searchRegex },
        { user: { $in: users.map(u => u._id) } }
      ];
      
      query = Teacher.find(filter)
        .populate('user', 'profile preferences')
        .populate('subjects', 'name nameSinhala code')
        .populate('classes', 'name nameSinhala grade section')
        .populate('classTeacherOf', 'name nameSinhala grade section')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit);
    }

    const teachers = await query;
    const total = await Teacher.countDocuments(filter);

    res.json({
      message: 'Teachers retrieved successfully',
      messageSinhala: 'ගුරුවරුන් සාර්ථකව ලබා ගන්නා ලදී',
      teachers,
      pagination: {
        current: page,
        pages: Math.ceil(total / limit),
        total,
        limit
      }
    });
  } catch (error) {
    console.error('Get teachers error:', error);
    res.status(500).json({
      message: 'Failed to retrieve teachers',
      messageSinhala: 'ගුරුවරුන් ලබා ගැනීමට අසමත් විය'
    });
  }
});

// Get teacher by ID
router.get('/:id', authenticated, async (req, res) => {
  try {
    const teacher = await Teacher.findById(req.params.id)
      .populate('user', 'profile preferences email username')
      .populate('subjects', 'name nameSinhala code department')
      .populate('classes', 'name nameSinhala grade section capacity')
      .populate('classTeacherOf', 'name nameSinhala grade section capacity students');

    if (!teacher) {
      return res.status(404).json({
        message: 'Teacher not found',
        messageSinhala: 'ගුරුවරයා සොයා ගත නොහැක'
      });
    }

    res.json({
      message: 'Teacher retrieved successfully',
      messageSinhala: 'ගුරුවරයා සාර්ථකව ලබා ගන්නා ලදී',
      teacher
    });
  } catch (error) {
    console.error('Get teacher error:', error);
    res.status(500).json({
      message: 'Failed to retrieve teacher',
      messageSinhala: 'ගුරුවරයා ලබා ගැනීමට අසමත් විය'
    });
  }
});

// Create new teacher
router.post('/', adminOnly, [
  body('employeeId').notEmpty().trim(),
  body('user').isMongoId(),
  body('short').optional().trim(),
  body('fullName').notEmpty().trim(),
  body('fullNameSinhala').optional().trim(),
  body('subjects').optional().isArray(),
  body('subjects.*').optional().isMongoId(),
  body('color').optional().matches(/^#[0-9A-F]{6}$/i),
  body('customFields').optional().isArray(),
  body('customFields.*.name').optional().notEmpty().trim(),
  body('customFields.*.value').optional().exists(),
  body('customFields.*.type').optional().isIn(['text', 'number', 'boolean', 'date', 'select']),
  body('personalInfo.title').optional().isIn(['Mr.', 'Mrs.', 'Miss', 'Dr.', 'Prof.']),
  body('personalInfo.dateOfBirth').optional().isISO8601(),
  body('personalInfo.gender').optional().isIn(['male', 'female', 'other']),
  body('personalInfo.nationality').optional().notEmpty().trim(),
  body('personalInfo.nic').optional().notEmpty().trim(),
  body('qualifications').optional().isArray(),
  body('experience.totalYears').optional().isInt({ min: 0 }),
  body('experience.currentSchoolYears').optional().isInt({ min: 0 }),
  body('workingHours.maxPeriodsPerDay').optional().isInt({ min: 1, max: 10 }),
  body('workingHours.maxPeriodsPerWeek').optional().isInt({ min: 1, max: 50 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: 'Validation errors',
        messageSinhala: 'වලංගුකරණ දෝෂ',
        errors: errors.array()
      });
    }

    // Check if employee ID already exists
    const existingTeacher = await Teacher.findOne({ employeeId: req.body.employeeId });
    if (existingTeacher) {
      return res.status(400).json({
        message: 'Employee ID already exists',
        messageSinhala: 'සේවක හැඳුනුම්පත් අංකය දැනටමත් පවතී'
      });
    }

    // Check if user exists and is not already a teacher
    const user = await User.findById(req.body.user);
    if (!user) {
      return res.status(400).json({
        message: 'User not found',
        messageSinhala: 'පරිශීලකයා සොයා ගත නොහැක'
      });
    }

    const existingTeacherUser = await Teacher.findOne({ user: req.body.user });
    if (existingTeacherUser) {
      return res.status(400).json({
        message: 'User is already a teacher',
        messageSinhala: 'පරිශීලකයා දැනටමත් ගුරුවරයෙකි'
      });
    }

    const teacher = new Teacher(req.body);
    await teacher.save();

    // Update user role to teacher if not already
    if (user.role !== 'teacher') {
      user.role = 'teacher';
      await user.save();
    }

    const populatedTeacher = await Teacher.findById(teacher._id)
      .populate('user', 'profile preferences email username')
      .populate('subjects', 'name nameSinhala code')
      .populate('classes', 'name nameSinhala grade section');

    res.status(201).json({
      message: 'Teacher created successfully',
      messageSinhala: 'ගුරුවරයා සාර්ථකව නිර්මාණය කරන ලදී',
      teacher: populatedTeacher
    });
  } catch (error) {
    console.error('Create teacher error:', error);
    res.status(500).json({
      message: 'Failed to create teacher',
      messageSinhala: 'ගුරුවරයා නිර්මාණය කිරීමට අසමත් විය'
    });
  }
});

// Update teacher
router.put('/:id', adminOnly, [
  body('employeeId').optional().notEmpty().trim(),
  body('subjects').optional().isArray(),
  body('subjects.*').optional().isMongoId(),
  body('classes').optional().isArray(),
  body('classes.*').optional().isMongoId(),
  body('maxPeriodsPerDay').optional().isInt({ min: 1, max: 10 }),
  body('maxPeriodsPerWeek').optional().isInt({ min: 1, max: 50 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: 'Validation errors',
        messageSinhala: 'වලංගුකරණ දෝෂ',
        errors: errors.array()
      });
    }

    // Check if employee ID is being changed and if it already exists
    if (req.body.employeeId) {
      const existingTeacher = await Teacher.findOne({ 
        employeeId: req.body.employeeId,
        _id: { $ne: req.params.id }
      });
      if (existingTeacher) {
        return res.status(400).json({
          message: 'Employee ID already exists',
          messageSinhala: 'සේවක හැඳුනුම්පත් අංකය දැනටමත් පවතී'
        });
      }
    }

    const teacher = await Teacher.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    ).populate('user', 'profile preferences email username')
     .populate('subjects', 'name nameSinhala code')
     .populate('classes', 'name nameSinhala grade section')
     .populate('classTeacherOf', 'name nameSinhala grade section');

    if (!teacher) {
      return res.status(404).json({
        message: 'Teacher not found',
        messageSinhala: 'ගුරුවරයා සොයා ගත නොහැක'
      });
    }

    res.json({
      message: 'Teacher updated successfully',
      messageSinhala: 'ගුරුවරයා සාර්ථකව යාවත්කාලීන කරන ලදී',
      teacher
    });
  } catch (error) {
    console.error('Update teacher error:', error);
    res.status(500).json({
      message: 'Failed to update teacher',
      messageSinhala: 'ගුරුවරයා යාවත්කාලීන කිරීමට අසමත් විය'
    });
  }
});

// Delete teacher (soft delete)
router.delete('/:id', adminOnly, async (req, res) => {
  try {
    const teacher = await Teacher.findByIdAndUpdate(
      req.params.id,
      { isActive: false },
      { new: true }
    );

    if (!teacher) {
      return res.status(404).json({
        message: 'Teacher not found',
        messageSinhala: 'ගුරුවරයා සොයා ගත නොහැක'
      });
    }

    res.json({
      message: 'Teacher deactivated successfully',
      messageSinhala: 'ගුරුවරයා සාර්ථකව අක්‍රිය කරන ලදී'
    });
  } catch (error) {
    console.error('Delete teacher error:', error);
    res.status(500).json({
      message: 'Failed to deactivate teacher',
      messageSinhala: 'ගුරුවරයා අක්‍රිය කිරීමට අසමත් විය'
    });
  }
});

module.exports = router;
