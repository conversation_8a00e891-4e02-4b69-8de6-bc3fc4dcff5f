const mongoose = require('mongoose');

// Custom field schema for flexible room properties
const customFieldSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  value: {
    type: mongoose.Schema.Types.Mixed,
    required: true
  },
  type: {
    type: String,
    enum: ['text', 'number', 'boolean', 'date', 'select'],
    default: 'text'
  },
  options: [String] // For select type fields
}, { _id: false });

const roomSchema = new mongoose.Schema({
  // Classroom name (as shown in screenshot)
  classroomName: {
    type: String,
    required: true,
    trim: true
  },
  number: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  name: {
    type: String,
    required: true,
    trim: true
  },
  nameSinhala: {
    type: String,
    trim: true
  },
  // Short identifier for the classroom
  short: {
    type: String,
    required: true,
    trim: true,
    uppercase: true,
    maxlength: 10
  },
  type: {
    type: String,
    required: true,
    enum: ['classroom', 'laboratory', 'computer_lab', 'library', 'auditorium', 'gymnasium', 'art_room', 'music_room', 'staff_room', 'office']
  },
  // Name classroom checkbox (as shown in screenshot)
  nameClassroom: {
    type: Boolean,
    default: true
  },
  // Shared room checkbox
  sharedRoom: {
    type: Boolean,
    default: false
  },
  // This room may be a classroom checkbox
  mayBeClassroom: {
    type: Boolean,
    default: true
  },
  building: {
    type: String,
    required: true,
    trim: true
  },
  floor: {
    type: Number,
    required: true,
    min: 0,
    max: 10
  },
  capacity: {
    type: Number,
    required: true,
    min: 1,
    max: 100
  },
  // Color for visual identification in timetables
  color: {
    type: String,
    default: '#4CAF50', // Material Design Green
    match: /^#[0-9A-F]{6}$/i
  },
  facilities: {
    hasProjector: {
      type: Boolean,
      default: false
    },
    hasComputers: {
      type: Boolean,
      default: false
    },
    computerCount: {
      type: Number,
      default: 0
    },
    hasAirConditioning: {
      type: Boolean,
      default: false
    },
    hasWhiteboard: {
      type: Boolean,
      default: true
    },
    hasSmartBoard: {
      type: Boolean,
      default: false
    },
    hasAudioSystem: {
      type: Boolean,
      default: false
    },
    specialEquipment: [String]
  },
  // Custom fields for additional room properties
  customFields: [customFieldSchema],
  subjects: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Subject'
  }],
  assignedClass: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Class'
  },
  availability: {
    monday: [{
      period: Number,
      isAvailable: Boolean,
      bookedBy: String,
      reason: String
    }],
    tuesday: [{
      period: Number,
      isAvailable: Boolean,
      bookedBy: String,
      reason: String
    }],
    wednesday: [{
      period: Number,
      isAvailable: Boolean,
      bookedBy: String,
      reason: String
    }],
    thursday: [{
      period: Number,
      isAvailable: Boolean,
      bookedBy: String,
      reason: String
    }],
    friday: [{
      period: Number,
      isAvailable: Boolean,
      bookedBy: String,
      reason: String
    }],
    saturday: [{
      period: Number,
      isAvailable: Boolean,
      bookedBy: String,
      reason: String
    }]
  },
  maintenanceSchedule: [{
    date: Date,
    description: String,
    isCompleted: {
      type: Boolean,
      default: false
    }
  }],
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Indexes
roomSchema.index({ number: 1 });
roomSchema.index({ type: 1 });
roomSchema.index({ building: 1, floor: 1 });
roomSchema.index({ assignedClass: 1 });

// Virtual for display name
roomSchema.virtual('displayName').get(function() {
  return this.nameSinhala || this.name;
});

roomSchema.set('toJSON', { virtuals: true });

module.exports = mongoose.model('Room', roomSchema);
