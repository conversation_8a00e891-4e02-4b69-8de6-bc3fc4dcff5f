const mongoose = require('mongoose');

// Custom field schema for flexible subject properties
const customFieldSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  value: {
    type: mongoose.Schema.Types.Mixed,
    required: true
  },
  type: {
    type: String,
    enum: ['text', 'number', 'boolean', 'date', 'select'],
    default: 'text'
  },
  options: [String] // For select type fields
}, { _id: false });

const subjectSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  nameSinhala: {
    type: String,
    required: true,
    trim: true
  },
  code: {
    type: String,
    required: true,
    unique: true,
    uppercase: true,
    trim: true
  },
  department: {
    type: String,
    required: true,
    enum: ['mathematics', 'science', 'languages', 'social_studies', 'arts', 'physical_education', 'technology', 'religion']
  },
  grade: {
    type: Number,
    required: true,
    min: 1,
    max: 13
  },
  periodsPerWeek: {
    type: Number,
    required: true,
    min: 1,
    max: 10
  },
  duration: {
    type: Number,
    default: 40, // minutes
    min: 30,
    max: 80
  },
  isCore: {
    type: Boolean,
    default: false
  },
  isOptional: {
    type: Boolean,
    default: false
  },
  prerequisites: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Subject'
  }],
  resources: {
    requiresLab: {
      type: Boolean,
      default: false
    },
    requiresComputer: {
      type: Boolean,
      default: false
    },
    requiresProjector: {
      type: Boolean,
      default: false
    },
    specialEquipment: [String]
  },
  color: {
    type: String,
    default: '#2196F3', // Material Design Blue
    match: /^#[0-9A-F]{6}$/i
  },
  description: {
    type: String,
    trim: true
  },
  descriptionSinhala: {
    type: String,
    trim: true
  },
  // Custom fields for additional subject properties
  customFields: [customFieldSchema],
  // Subject scheduling preferences
  preferences: {
    preferredPeriods: [{
      day: {
        type: String,
        enum: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday']
      },
      periods: [Number]
    }],
    avoidPeriods: [{
      day: {
        type: String,
        enum: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday']
      },
      periods: [Number],
      reason: String
    }],
    consecutivePeriods: {
      type: Boolean,
      default: false
    },
    maxConsecutive: {
      type: Number,
      default: 2
    }
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Indexes
subjectSchema.index({ code: 1 });
subjectSchema.index({ grade: 1 });
subjectSchema.index({ department: 1 });
subjectSchema.index({ name: 'text', nameSinhala: 'text' });

// Virtual for display name based on language
subjectSchema.virtual('displayName').get(function() {
  return this.nameSinhala || this.name;
});

subjectSchema.set('toJSON', { virtuals: true });

module.exports = mongoose.model('Subject', subjectSchema);
